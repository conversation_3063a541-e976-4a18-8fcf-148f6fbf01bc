/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5COneDrive%5C%5CDesktop%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CAboutSection.tsx%22%2C%22ids%22%3A%5B%22AboutSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5COneDrive%5C%5CDesktop%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CHeroSection.tsx%22%2C%22ids%22%3A%5B%22HeroSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5COneDrive%5C%5CDesktop%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CProjectsSection.tsx%22%2C%22ids%22%3A%5B%22ProjectsSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5COneDrive%5C%5CDesktop%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CSkillsSection.tsx%22%2C%22ids%22%3A%5B%22SkillsSection%22%5D%7D&server=false!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5COneDrive%5C%5CDesktop%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CAboutSection.tsx%22%2C%22ids%22%3A%5B%22AboutSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5COneDrive%5C%5CDesktop%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CHeroSection.tsx%22%2C%22ids%22%3A%5B%22HeroSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5COneDrive%5C%5CDesktop%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CProjectsSection.tsx%22%2C%22ids%22%3A%5B%22ProjectsSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5COneDrive%5C%5CDesktop%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CSkillsSection.tsx%22%2C%22ids%22%3A%5B%22SkillsSection%22%5D%7D&server=false! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/sections/AboutSection.tsx */ \"(app-pages-browser)/./src/components/sections/AboutSection.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/sections/HeroSection.tsx */ \"(app-pages-browser)/./src/components/sections/HeroSection.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/sections/ProjectsSection.tsx */ \"(app-pages-browser)/./src/components/sections/ProjectsSection.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/sections/SkillsSection.tsx */ \"(app-pages-browser)/./src/components/sections/SkillsSection.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5COneDrive%5C%5CDesktop%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CAboutSection.tsx%22%2C%22ids%22%3A%5B%22AboutSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5COneDrive%5C%5CDesktop%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CHeroSection.tsx%22%2C%22ids%22%3A%5B%22HeroSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5COneDrive%5C%5CDesktop%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CProjectsSection.tsx%22%2C%22ids%22%3A%5B%22ProjectsSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5COneDrive%5C%5CDesktop%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CSkillsSection.tsx%22%2C%22ids%22%3A%5B%22SkillsSection%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/sections/ProjectsSection.tsx":
/*!*****************************************************!*\
  !*** ./src/components/sections/ProjectsSection.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProjectsSection: () => (/* binding */ ProjectsSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var gsap__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! gsap */ \"(app-pages-browser)/./node_modules/gsap/index.js\");\n/* harmony import */ var gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! gsap/ScrollTrigger */ \"(app-pages-browser)/./node_modules/gsap/ScrollTrigger.js\");\n/* __next_internal_client_entry_do_not_use__ ProjectsSection auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nif (true) {\n    gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.registerPlugin(gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_3__.ScrollTrigger);\n}\nconst projects = [\n    {\n        id: 1,\n        title: \"Multimodal Deepfake Detection\",\n        description: \"Advanced AI system for detecting deepfake content using computer vision and machine learning. Combines multiple detection techniques for improved accuracy.\",\n        image: \"/api/placeholder/400/250\",\n        technologies: [\n            \"Python\",\n            \"TensorFlow\",\n            \"OpenCV\",\n            \"React\",\n            \"Node.js\"\n        ],\n        category: \"AI/ML\",\n        status: \"In Progress\",\n        github: \"https://github.com/yourusername/deepfake-detection\",\n        demo: null,\n        features: [\n            \"Multi-modal detection approach\",\n            \"Real-time processing\",\n            \"High accuracy rate\",\n            \"User-friendly interface\"\n        ]\n    },\n    {\n        id: 2,\n        title: \"E-Commerce MERN App\",\n        description: \"Full-stack e-commerce platform with user authentication, payment integration, admin dashboard, and real-time inventory management.\",\n        image: \"/api/placeholder/400/250\",\n        technologies: [\n            \"React\",\n            \"Node.js\",\n            \"MongoDB\",\n            \"Express\",\n            \"Stripe\",\n            \"JWT\"\n        ],\n        category: \"Full Stack\",\n        status: \"Completed\",\n        github: \"https://github.com/yourusername/ecommerce-app\",\n        demo: \"https://your-ecommerce-demo.netlify.app\",\n        features: [\n            \"User authentication & authorization\",\n            \"Payment gateway integration\",\n            \"Admin dashboard\",\n            \"Real-time inventory tracking\"\n        ]\n    },\n    {\n        id: 3,\n        title: \"Task Management Dashboard\",\n        description: \"Collaborative task management application with real-time updates, team collaboration features, and advanced project tracking.\",\n        image: \"/api/placeholder/400/250\",\n        technologies: [\n            \"React\",\n            \"TypeScript\",\n            \"Node.js\",\n            \"Socket.io\",\n            \"MongoDB\"\n        ],\n        category: \"Full Stack\",\n        status: \"Completed\",\n        github: \"https://github.com/yourusername/task-manager\",\n        demo: \"https://your-taskmanager-demo.netlify.app\",\n        features: [\n            \"Real-time collaboration\",\n            \"Project timeline tracking\",\n            \"Team management\",\n            \"File sharing capabilities\"\n        ]\n    },\n    {\n        id: 4,\n        title: \"Weather Forecast App\",\n        description: \"Modern weather application with location-based forecasts, interactive maps, and detailed weather analytics using external APIs.\",\n        image: \"/api/placeholder/400/250\",\n        technologies: [\n            \"React\",\n            \"JavaScript\",\n            \"OpenWeather API\",\n            \"Chart.js\",\n            \"CSS3\"\n        ],\n        category: \"Frontend\",\n        status: \"Completed\",\n        github: \"https://github.com/yourusername/weather-app\",\n        demo: \"https://your-weather-demo.netlify.app\",\n        features: [\n            \"Location-based forecasts\",\n            \"Interactive weather maps\",\n            \"7-day forecast\",\n            \"Weather analytics charts\"\n        ]\n    },\n    {\n        id: 5,\n        title: \"Blog Platform\",\n        description: \"Full-featured blogging platform with rich text editor, comment system, user profiles, and content management capabilities.\",\n        image: \"/api/placeholder/400/250\",\n        technologies: [\n            \"React\",\n            \"Node.js\",\n            \"MongoDB\",\n            \"Express\",\n            \"Quill.js\"\n        ],\n        category: \"Full Stack\",\n        status: \"Completed\",\n        github: \"https://github.com/yourusername/blog-platform\",\n        demo: \"https://your-blog-demo.netlify.app\",\n        features: [\n            \"Rich text editor\",\n            \"Comment system\",\n            \"User profiles\",\n            \"Content categorization\"\n        ]\n    },\n    {\n        id: 6,\n        title: \"Portfolio Website\",\n        description: \"Responsive portfolio website built with Next.js, featuring smooth animations, dark mode, and optimized performance.\",\n        image: \"/api/placeholder/400/250\",\n        technologies: [\n            \"Next.js\",\n            \"TypeScript\",\n            \"Tailwind CSS\",\n            \"GSAP\",\n            \"Framer Motion\"\n        ],\n        category: \"Frontend\",\n        status: \"Completed\",\n        github: \"https://github.com/yourusername/portfolio\",\n        demo: \"https://your-portfolio.vercel.app\",\n        features: [\n            \"Responsive design\",\n            \"Smooth animations\",\n            \"Dark mode toggle\",\n            \"SEO optimized\"\n        ]\n    }\n];\nfunction ProjectsSection() {\n    _s();\n    const sectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const titleRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const projectsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProjectsSection.useEffect\": ()=>{\n            const ctx = gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.context({\n                \"ProjectsSection.useEffect.ctx\": ()=>{\n                    var _projectsRef_current;\n                    // Title animation\n                    gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.fromTo(titleRef.current, {\n                        opacity: 0,\n                        y: 50\n                    }, {\n                        opacity: 1,\n                        y: 0,\n                        duration: 1,\n                        ease: \"power3.out\",\n                        scrollTrigger: {\n                            trigger: titleRef.current,\n                            start: \"top 80%\",\n                            end: \"bottom 20%\",\n                            toggleActions: \"play none none reverse\"\n                        }\n                    });\n                    // Projects stagger animation\n                    gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.fromTo(((_projectsRef_current = projectsRef.current) === null || _projectsRef_current === void 0 ? void 0 : _projectsRef_current.children) || [], {\n                        opacity: 0,\n                        y: 50,\n                        scale: 0.9\n                    }, {\n                        opacity: 1,\n                        y: 0,\n                        scale: 1,\n                        duration: 0.8,\n                        ease: \"power3.out\",\n                        stagger: 0.2,\n                        scrollTrigger: {\n                            trigger: projectsRef.current,\n                            start: \"top 80%\",\n                            end: \"bottom 20%\",\n                            toggleActions: \"play none none reverse\"\n                        }\n                    });\n                }\n            }[\"ProjectsSection.useEffect.ctx\"], sectionRef);\n            return ({\n                \"ProjectsSection.useEffect\": ()=>ctx.revert()\n            })[\"ProjectsSection.useEffect\"];\n        }\n    }[\"ProjectsSection.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"projects\",\n        ref: sectionRef,\n        className: \"py-20 bg-white dark:bg-gray-900\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            ref: titleRef,\n                            className: \"text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4\",\n                            children: [\n                                \"My \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"gradient-text\",\n                                    children: \"Projects\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\ProjectsSection.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 16\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\ProjectsSection.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto\",\n                            children: \"Here are some of the projects I've worked on, showcasing my skills in full-stack development and AI/ML\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\ProjectsSection.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\ProjectsSection.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    ref: projectsRef,\n                    className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    children: projects.map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"group bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 overflow-hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-48 bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-semibold text-lg\",\n                                                children: project.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\ProjectsSection.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\ProjectsSection.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-4 right-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-3 py-1 rounded-full text-xs font-medium \".concat(project.status === 'Completed' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'),\n                                                children: project.status\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\ProjectsSection.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\ProjectsSection.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\ProjectsSection.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-200\",\n                                                    children: project.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\ProjectsSection.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-blue-600 dark:text-blue-400 font-medium\",\n                                                    children: project.category\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\ProjectsSection.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\ProjectsSection.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-300 text-sm mb-4 line-clamp-3\",\n                                            children: project.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\ProjectsSection.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2 mb-4\",\n                                            children: [\n                                                project.technologies.slice(0, 3).map((tech)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded text-xs font-medium\",\n                                                        children: tech\n                                                    }, tech, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\ProjectsSection.tsx\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 21\n                                                    }, this)),\n                                                project.technologies.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400 rounded text-xs\",\n                                                    children: [\n                                                        \"+\",\n                                                        project.technologies.length - 3,\n                                                        \" more\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\ProjectsSection.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\ProjectsSection.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: project.github,\n                                                    target: \"_blank\",\n                                                    rel: \"noopener noreferrer\",\n                                                    className: \"flex items-center px-3 py-2 bg-gray-800 dark:bg-gray-700 text-white rounded-lg hover:bg-gray-900 dark:hover:bg-gray-600 transition-colors duration-200 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\ProjectsSection.tsx\",\n                                                            lineNumber: 249,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Code\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\ProjectsSection.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 19\n                                                }, this),\n                                                project.demo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: project.demo,\n                                                    target: \"_blank\",\n                                                    rel: \"noopener noreferrer\",\n                                                    className: \"flex items-center px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\ProjectsSection.tsx\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"Demo\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\ProjectsSection.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\ProjectsSection.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\ProjectsSection.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, project.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\ProjectsSection.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\ProjectsSection.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mt-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"https://github.com/yourusername\",\n                        target: \"_blank\",\n                        rel: \"noopener noreferrer\",\n                        className: \"inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-semibold transition-colors duration-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-5 w-5 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\ProjectsSection.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 13\n                            }, this),\n                            \"View More on GitHub\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\ProjectsSection.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\ProjectsSection.tsx\",\n                    lineNumber: 270,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\ProjectsSection.tsx\",\n            lineNumber: 172,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\ProjectsSection.tsx\",\n        lineNumber: 167,\n        columnNumber: 5\n    }, this);\n}\n_s(ProjectsSection, \"1cIguBcLaz0dNBwhTzXVfggYf7g=\");\n_c = ProjectsSection;\nvar _c;\n$RefreshReg$(_c, \"ProjectsSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/ProjectsSection.tsx\n"));

/***/ })

});