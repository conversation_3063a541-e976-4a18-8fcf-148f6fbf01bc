"use client";

import { useEffect, useRef } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

if (typeof window !== "undefined") {
  gsap.registerPlugin(ScrollTrigger);
}

const skillsData = {
  Frontend: [
    { name: "React", level: 90, color: "bg-blue-500" },
    { name: "JavaScript", level: 85, color: "bg-yellow-500" },
    { name: "HTML5", level: 95, color: "bg-orange-500" },
    { name: "CSS3", level: 90, color: "bg-blue-600" },
    { name: "Tailwind CSS", level: 85, color: "bg-teal-500" },
    { name: "TypeScript", level: 75, color: "bg-blue-700" },
  ],
  Backend: [
    { name: "Node.js", level: 85, color: "bg-green-600" },
    { name: "Express.js", level: 80, color: "bg-gray-600" },
    { name: "REST APIs", level: 85, color: "bg-purple-500" },
    { name: "Authentication", level: 75, color: "bg-red-500" },
  ],
  Database: [
    { name: "MongoDB", level: 80, color: "bg-green-500" },
    { name: "Mongoose", level: 75, color: "bg-green-600" },
    { name: "MySQL", level: 70, color: "bg-blue-500" },
  ],
  Tools: [
    { name: "Git & GitHub", level: 85, color: "bg-gray-800" },
    { name: "VS Code", level: 90, color: "bg-blue-600" },
    { name: "Postman", level: 80, color: "bg-orange-500" },
    { name: "Netlify/Vercel", level: 75, color: "bg-black" },
    { name: "npm/yarn", level: 80, color: "bg-red-600" },
  ],
};

export function SkillsSection() {
  const sectionRef = useRef<HTMLElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const categoriesRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Title animation
      gsap.fromTo(
        titleRef.current,
        { opacity: 0, y: 50 },
        {
          opacity: 1,
          y: 0,
          duration: 1,
          ease: "power3.out",
          scrollTrigger: {
            trigger: titleRef.current,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse",
          },
        }
      );

      // Categories animation
      gsap.fromTo(
        categoriesRef.current?.children || [],
        { opacity: 0, y: 50 },
        {
          opacity: 1,
          y: 0,
          duration: 0.8,
          ease: "power3.out",
          stagger: 0.2,
          scrollTrigger: {
            trigger: categoriesRef.current,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse",
          },
        }
      );

      // Skill bars animation
      Object.keys(skillsData).forEach((category, categoryIndex) => {
        const categoryElement = categoriesRef.current?.children[categoryIndex];
        const skillBars = categoryElement?.querySelectorAll('.skill-bar');
        
        if (skillBars) {
          skillBars.forEach((bar, index) => {
            const progressBar = bar.querySelector('.progress-bar');
            const skill = skillsData[category as keyof typeof skillsData][index];
            
            if (progressBar) {
              gsap.fromTo(
                progressBar,
                { width: "0%" },
                {
                  width: `${skill.level}%`,
                  duration: 1.5,
                  ease: "power3.out",
                  delay: 0.5 + (index * 0.1),
                  scrollTrigger: {
                    trigger: bar,
                    start: "top 90%",
                    end: "bottom 10%",
                    toggleActions: "play none none reverse",
                  },
                }
              );
            }
          });
        }
      });
    }, sectionRef);

    return () => ctx.revert();
  }, []);

  return (
    <section
      id="skills"
      ref={sectionRef}
      className="py-20 bg-gray-50 dark:bg-gray-800"
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2
            ref={titleRef}
            className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4"
          >
            My <span className="gradient-text">Skills</span>
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Here are the technologies and tools I work with to bring ideas to life
          </p>
        </div>

        <div ref={categoriesRef} className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {Object.entries(skillsData).map(([category, skills]) => (
            <div
              key={category}
              className="bg-white dark:bg-gray-900 p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300"
            >
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-6 text-center">
                {category}
              </h3>
              <div className="space-y-4">
                {skills.map((skill, index) => (
                  <div key={skill.name} className="skill-bar">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        {skill.name}
                      </span>
                      <span className="text-sm text-gray-500 dark:text-gray-400">
                        {skill.level}%
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div
                        className={`progress-bar h-2 rounded-full ${skill.color} transition-all duration-300`}
                        style={{ width: "0%" }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Additional Skills */}
        <div className="mt-16 text-center">
          <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-8">
            Additional Technologies
          </h3>
          <div className="flex flex-wrap justify-center gap-4">
            {[
              "Python",
              "Machine Learning",
              "Computer Vision",
              "Docker",
              "Linux",
              "Figma",
              "Photoshop",
              "Responsive Design",
              "SEO",
              "Performance Optimization",
            ].map((tech) => (
              <span
                key={tech}
                className="px-4 py-2 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded-full text-sm font-medium hover:bg-blue-200 dark:hover:bg-blue-900/50 transition-colors duration-200"
              >
                {tech}
              </span>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
