"use client";

import { useEffect, useRef } from "react";
import { Code, GraduationCap, Heart, Lightbulb } from "lucide-react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

if (typeof window !== "undefined") {
  gsap.registerPlugin(ScrollTrigger);
}

export function AboutSection() {
  const sectionRef = useRef<HTMLElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const cardsRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Title animation
      gsap.fromTo(
        titleRef.current,
        { opacity: 0, y: 50 },
        {
          opacity: 1,
          y: 0,
          duration: 1,
          ease: "power3.out",
          scrollTrigger: {
            trigger: titleRef.current,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse",
          },
        }
      );

      // Content animation
      gsap.fromTo(
        contentRef.current,
        { opacity: 0, y: 30 },
        {
          opacity: 1,
          y: 0,
          duration: 0.8,
          ease: "power3.out",
          scrollTrigger: {
            trigger: contentRef.current,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse",
          },
        }
      );

      // Cards stagger animation
      gsap.fromTo(
        cardsRef.current?.children || [],
        { opacity: 0, y: 50, scale: 0.9 },
        {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 0.6,
          ease: "power3.out",
          stagger: 0.2,
          scrollTrigger: {
            trigger: cardsRef.current,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse",
          },
        }
      );
    }, sectionRef);

    return () => ctx.revert();
  }, []);

  return (
    <section
      id="about"
      ref={sectionRef}
      className="py-20 bg-white dark:bg-gray-900"
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2
            ref={titleRef}
            className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4"
          >
            About <span className="gradient-text">Me</span>
          </h2>
        </div>

        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div ref={contentRef} className="space-y-6">
            <div className="prose prose-lg dark:prose-invert max-w-none">
              <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                Hello! I'm Aman, a passionate MERN Stack Developer currently pursuing my 
                <strong className="text-blue-600 dark:text-blue-400"> Master of Computer Applications (MCA) at Thapar University</strong>. 
                My journey in web development started with curiosity and has evolved into a deep passion for creating 
                meaningful digital experiences.
              </p>
              
              <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                I specialize in building modern, responsive web applications using the MERN stack 
                (MongoDB, Express.js, React, Node.js). What drives me is the ability to transform 
                ideas into functional, beautiful applications that solve real-world problems.
              </p>

              <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                Currently, I'm working on my capstone project focused on 
                <strong className="text-purple-600 dark:text-purple-400"> Multimodal Deepfake Detection</strong>, 
                exploring the intersection of AI and cybersecurity. This project combines my technical skills 
                with cutting-edge research in machine learning and computer vision.
              </p>

              <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                When I'm not coding, you'll find me exploring new technologies, contributing to open-source projects, 
                or sharing my learning journey through technical blogs. I believe in continuous learning and 
                staying updated with the latest trends in web development.
              </p>
            </div>
          </div>

          {/* Info Cards */}
          <div ref={cardsRef} className="grid gap-6">
            <div className="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 p-6 rounded-xl border border-blue-200 dark:border-blue-800">
              <div className="flex items-center mb-4">
                <div className="p-3 bg-blue-600 rounded-lg mr-4">
                  <Code className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                  Development Focus
                </h3>
              </div>
              <p className="text-gray-600 dark:text-gray-300">
                Specializing in MERN Stack development with a focus on clean, scalable code 
                and modern web technologies.
              </p>
            </div>

            <div className="bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 p-6 rounded-xl border border-purple-200 dark:border-purple-800">
              <div className="flex items-center mb-4">
                <div className="p-3 bg-purple-600 rounded-lg mr-4">
                  <GraduationCap className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                  Education
                </h3>
              </div>
              <p className="text-gray-600 dark:text-gray-300">
                MCA student at Thapar University, combining academic excellence with 
                practical development experience.
              </p>
            </div>

            <div className="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 p-6 rounded-xl border border-green-200 dark:border-green-800">
              <div className="flex items-center mb-4">
                <div className="p-3 bg-green-600 rounded-lg mr-4">
                  <Lightbulb className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                  Current Research
                </h3>
              </div>
              <p className="text-gray-600 dark:text-gray-300">
                Working on Multimodal Deepfake Detection, exploring AI and machine learning 
                applications in cybersecurity.
              </p>
            </div>

            <div className="bg-gradient-to-r from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 p-6 rounded-xl border border-red-200 dark:border-red-800">
              <div className="flex items-center mb-4">
                <div className="p-3 bg-red-600 rounded-lg mr-4">
                  <Heart className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                  Passion
                </h3>
              </div>
              <p className="text-gray-600 dark:text-gray-300">
                Passionate about creating impactful web applications and contributing to 
                the open-source community.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
