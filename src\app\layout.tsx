import type { Metadata } from "next";
import "./globals.css";
import { Navigation } from "@/components/Navigation";
import { ThemeProvider } from "@/components/ThemeProvider";

export const metadata: Metadata = {
  title: "Aman - MERN Stack Developer",
  description: "<PERSON><PERSON><PERSON> of <PERSON><PERSON>, a passionate MERN Stack Developer and MCA student at Thapar University. Specializing in React, Node.js, MongoDB, and modern web development.",
  keywords: ["MERN Stack", "React", "Node.js", "MongoDB", "Web Developer", "Portfolio", "Thapar University"],
  authors: [{ name: "<PERSON><PERSON>" }],
  openGraph: {
    title: "Aman - MERN Stack Developer",
    description: "<PERSON><PERSON><PERSON> of <PERSON><PERSON>, a passionate MERN Stack Developer and MCA student at Thapar University.",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "Aman - MERN Stack Developer",
    description: "<PERSON><PERSON><PERSON> of <PERSON><PERSON>, a passionate MERN Stack Developer and MCA student at Thapar University.",
  },
};

export const viewport = {
  width: "device-width",
  initialScale: 1,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className="antialiased">
        <ThemeProvider>
          <Navigation />
          <main className="min-h-screen">
            {children}
          </main>
        </ThemeProvider>
      </body>
    </html>
  );
}
