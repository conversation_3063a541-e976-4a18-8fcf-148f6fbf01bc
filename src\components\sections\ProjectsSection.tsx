"use client";

import { useEffect, useRef } from "react";
import { ExternalLink, Github, Calendar, Users } from "lucide-react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

if (typeof window !== "undefined") {
  gsap.registerPlugin(ScrollTrigger);
}

const projects = [
  {
    id: 1,
    title: "Multimodal Deepfake Detection",
    description: "Advanced AI system for detecting deepfake content using computer vision and machine learning. Combines multiple detection techniques for improved accuracy.",
    image: "/api/placeholder/400/250",
    technologies: ["Python", "TensorFlow", "OpenCV", "React", "Node.js"],
    category: "AI/ML",
    status: "In Progress",
    github: "https://github.com/yourusername/deepfake-detection",
    demo: null,
    features: [
      "Multi-modal detection approach",
      "Real-time processing",
      "High accuracy rate",
      "User-friendly interface"
    ]
  },
  {
    id: 2,
    title: "E-Commerce MERN App",
    description: "Full-stack e-commerce platform with user authentication, payment integration, admin dashboard, and real-time inventory management.",
    image: "/api/placeholder/400/250",
    technologies: ["React", "Node.js", "MongoDB", "Express", "Stripe", "JWT"],
    category: "Full Stack",
    status: "Completed",
    github: "https://github.com/yourusername/ecommerce-app",
    demo: "https://your-ecommerce-demo.netlify.app",
    features: [
      "User authentication & authorization",
      "Payment gateway integration",
      "Admin dashboard",
      "Real-time inventory tracking"
    ]
  },
  {
    id: 3,
    title: "Task Management Dashboard",
    description: "Collaborative task management application with real-time updates, team collaboration features, and advanced project tracking.",
    image: "/api/placeholder/400/250",
    technologies: ["React", "TypeScript", "Node.js", "Socket.io", "MongoDB"],
    category: "Full Stack",
    status: "Completed",
    github: "https://github.com/yourusername/task-manager",
    demo: "https://your-taskmanager-demo.netlify.app",
    features: [
      "Real-time collaboration",
      "Project timeline tracking",
      "Team management",
      "File sharing capabilities"
    ]
  },
  {
    id: 4,
    title: "Weather Forecast App",
    description: "Modern weather application with location-based forecasts, interactive maps, and detailed weather analytics using external APIs.",
    image: "/api/placeholder/400/250",
    technologies: ["React", "JavaScript", "OpenWeather API", "Chart.js", "CSS3"],
    category: "Frontend",
    status: "Completed",
    github: "https://github.com/yourusername/weather-app",
    demo: "https://your-weather-demo.netlify.app",
    features: [
      "Location-based forecasts",
      "Interactive weather maps",
      "7-day forecast",
      "Weather analytics charts"
    ]
  },
  {
    id: 5,
    title: "Blog Platform",
    description: "Full-featured blogging platform with rich text editor, comment system, user profiles, and content management capabilities.",
    image: "/api/placeholder/400/250",
    technologies: ["React", "Node.js", "MongoDB", "Express", "Quill.js"],
    category: "Full Stack",
    status: "Completed",
    github: "https://github.com/yourusername/blog-platform",
    demo: "https://your-blog-demo.netlify.app",
    features: [
      "Rich text editor",
      "Comment system",
      "User profiles",
      "Content categorization"
    ]
  },
  {
    id: 6,
    title: "Portfolio Website",
    description: "Responsive portfolio website built with Next.js, featuring smooth animations, dark mode, and optimized performance.",
    image: "/api/placeholder/400/250",
    technologies: ["Next.js", "TypeScript", "Tailwind CSS", "GSAP", "Framer Motion"],
    category: "Frontend",
    status: "Completed",
    github: "https://github.com/yourusername/portfolio",
    demo: "https://your-portfolio.vercel.app",
    features: [
      "Responsive design",
      "Smooth animations",
      "Dark mode toggle",
      "SEO optimized"
    ]
  }
];

export function ProjectsSection() {
  const sectionRef = useRef<HTMLElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const projectsRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Title animation
      gsap.fromTo(
        titleRef.current,
        { opacity: 0, y: 50 },
        {
          opacity: 1,
          y: 0,
          duration: 1,
          ease: "power3.out",
          scrollTrigger: {
            trigger: titleRef.current,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse",
          },
        }
      );

      // Projects stagger animation
      gsap.fromTo(
        projectsRef.current?.children || [],
        { opacity: 0, y: 50, scale: 0.9 },
        {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 0.8,
          ease: "power3.out",
          stagger: 0.2,
          scrollTrigger: {
            trigger: projectsRef.current,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse",
          },
        }
      );
    }, sectionRef);

    return () => ctx.revert();
  }, []);

  return (
    <section
      id="projects"
      ref={sectionRef}
      className="py-20 bg-white dark:bg-gray-900"
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2
            ref={titleRef}
            className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4"
          >
            My <span className="gradient-text">Projects</span>
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Here are some of the projects I've worked on, showcasing my skills in full-stack development and AI/ML
          </p>
        </div>

        <div ref={projectsRef} className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {projects.map((project) => (
            <div
              key={project.id}
              className="group bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 overflow-hidden"
            >
              {/* Project Image */}
              <div className="relative overflow-hidden">
                <div className="w-full h-48 bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
                  <span className="text-white font-semibold text-lg">
                    {project.title}
                  </span>
                </div>
                <div className="absolute top-4 right-4">
                  <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                    project.status === 'Completed' 
                      ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                      : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'
                  }`}>
                    {project.status}
                  </span>
                </div>
              </div>

              {/* Project Content */}
              <div className="p-6">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-200">
                    {project.title}
                  </h3>
                  <span className="text-sm text-blue-600 dark:text-blue-400 font-medium">
                    {project.category}
                  </span>
                </div>

                <p className="text-gray-600 dark:text-gray-300 text-sm mb-4 line-clamp-3">
                  {project.description}
                </p>

                {/* Technologies */}
                <div className="flex flex-wrap gap-2 mb-4">
                  {project.technologies.slice(0, 3).map((tech) => (
                    <span
                      key={tech}
                      className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded text-xs font-medium"
                    >
                      {tech}
                    </span>
                  ))}
                  {project.technologies.length > 3 && (
                    <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400 rounded text-xs">
                      +{project.technologies.length - 3} more
                    </span>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-3">
                  <a
                    href={project.github}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center px-3 py-2 bg-gray-800 dark:bg-gray-700 text-white rounded-lg hover:bg-gray-900 dark:hover:bg-gray-600 transition-colors duration-200 text-sm"
                  >
                    <Github className="h-4 w-4 mr-2" />
                    Code
                  </a>
                  {project.demo && (
                    <a
                      href={project.demo}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 text-sm"
                    >
                      <ExternalLink className="h-4 w-4 mr-2" />
                      Demo
                    </a>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* View More Projects */}
        <div className="text-center mt-12">
          <a
            href="https://github.com/yourusername"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-semibold transition-colors duration-200"
          >
            <Github className="h-5 w-5 mr-2" />
            View More on GitHub
          </a>
        </div>
      </div>
    </section>
  );
}
