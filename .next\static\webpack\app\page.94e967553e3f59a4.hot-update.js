/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/gsap/Observer.js":
/*!***************************************!*\
  !*** ./node_modules/gsap/Observer.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Observer: () => (/* binding */ Observer),\n/* harmony export */   _getProxyProp: () => (/* binding */ _getProxyProp),\n/* harmony export */   _getScrollFunc: () => (/* binding */ _getScrollFunc),\n/* harmony export */   _getTarget: () => (/* binding */ _getTarget),\n/* harmony export */   _getVelocityProp: () => (/* binding */ _getVelocityProp),\n/* harmony export */   _horizontal: () => (/* binding */ _horizontal),\n/* harmony export */   _isViewport: () => (/* binding */ _isViewport),\n/* harmony export */   _proxies: () => (/* binding */ _proxies),\n/* harmony export */   _scrollers: () => (/* binding */ _scrollers),\n/* harmony export */   _vertical: () => (/* binding */ _vertical),\n/* harmony export */   \"default\": () => (/* binding */ Observer)\n/* harmony export */ });\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\n/*!\n * Observer 3.13.0\n * https://gsap.com\n *\n * @license Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: Jack Doyle, <EMAIL>\n*/\n\n/* eslint-disable */\nvar gsap,\n    _coreInitted,\n    _clamp,\n    _win,\n    _doc,\n    _docEl,\n    _body,\n    _isTouch,\n    _pointerType,\n    ScrollTrigger,\n    _root,\n    _normalizer,\n    _eventTypes,\n    _context,\n    _getGSAP = function _getGSAP() {\n  return gsap || typeof window !== \"undefined\" && (gsap = window.gsap) && gsap.registerPlugin && gsap;\n},\n    _startup = 1,\n    _observers = [],\n    _scrollers = [],\n    _proxies = [],\n    _getTime = Date.now,\n    _bridge = function _bridge(name, value) {\n  return value;\n},\n    _integrate = function _integrate() {\n  var core = ScrollTrigger.core,\n      data = core.bridge || {},\n      scrollers = core._scrollers,\n      proxies = core._proxies;\n  scrollers.push.apply(scrollers, _scrollers);\n  proxies.push.apply(proxies, _proxies);\n  _scrollers = scrollers;\n  _proxies = proxies;\n\n  _bridge = function _bridge(name, value) {\n    return data[name](value);\n  };\n},\n    _getProxyProp = function _getProxyProp(element, property) {\n  return ~_proxies.indexOf(element) && _proxies[_proxies.indexOf(element) + 1][property];\n},\n    _isViewport = function _isViewport(el) {\n  return !!~_root.indexOf(el);\n},\n    _addListener = function _addListener(element, type, func, passive, capture) {\n  return element.addEventListener(type, func, {\n    passive: passive !== false,\n    capture: !!capture\n  });\n},\n    _removeListener = function _removeListener(element, type, func, capture) {\n  return element.removeEventListener(type, func, !!capture);\n},\n    _scrollLeft = \"scrollLeft\",\n    _scrollTop = \"scrollTop\",\n    _onScroll = function _onScroll() {\n  return _normalizer && _normalizer.isPressed || _scrollers.cache++;\n},\n    _scrollCacheFunc = function _scrollCacheFunc(f, doNotCache) {\n  var cachingFunc = function cachingFunc(value) {\n    // since reading the scrollTop/scrollLeft/pageOffsetY/pageOffsetX can trigger a layout, this function allows us to cache the value so it only gets read fresh after a \"scroll\" event fires (or while we're refreshing because that can lengthen the page and alter the scroll position). when \"soft\" is true, that means don't actually set the scroll, but cache the new value instead (useful in ScrollSmoother)\n    if (value || value === 0) {\n      _startup && (_win.history.scrollRestoration = \"manual\"); // otherwise the new position will get overwritten by the browser onload.\n\n      var isNormalizing = _normalizer && _normalizer.isPressed;\n      value = cachingFunc.v = Math.round(value) || (_normalizer && _normalizer.iOS ? 1 : 0); //TODO: iOS Bug: if you allow it to go to 0, Safari can start to report super strange (wildly inaccurate) touch positions!\n\n      f(value);\n      cachingFunc.cacheID = _scrollers.cache;\n      isNormalizing && _bridge(\"ss\", value); // set scroll (notify ScrollTrigger so it can dispatch a \"scrollStart\" event if necessary\n    } else if (doNotCache || _scrollers.cache !== cachingFunc.cacheID || _bridge(\"ref\")) {\n      cachingFunc.cacheID = _scrollers.cache;\n      cachingFunc.v = f();\n    }\n\n    return cachingFunc.v + cachingFunc.offset;\n  };\n\n  cachingFunc.offset = 0;\n  return f && cachingFunc;\n},\n    _horizontal = {\n  s: _scrollLeft,\n  p: \"left\",\n  p2: \"Left\",\n  os: \"right\",\n  os2: \"Right\",\n  d: \"width\",\n  d2: \"Width\",\n  a: \"x\",\n  sc: _scrollCacheFunc(function (value) {\n    return arguments.length ? _win.scrollTo(value, _vertical.sc()) : _win.pageXOffset || _doc[_scrollLeft] || _docEl[_scrollLeft] || _body[_scrollLeft] || 0;\n  })\n},\n    _vertical = {\n  s: _scrollTop,\n  p: \"top\",\n  p2: \"Top\",\n  os: \"bottom\",\n  os2: \"Bottom\",\n  d: \"height\",\n  d2: \"Height\",\n  a: \"y\",\n  op: _horizontal,\n  sc: _scrollCacheFunc(function (value) {\n    return arguments.length ? _win.scrollTo(_horizontal.sc(), value) : _win.pageYOffset || _doc[_scrollTop] || _docEl[_scrollTop] || _body[_scrollTop] || 0;\n  })\n},\n    _getTarget = function _getTarget(t, self) {\n  return (self && self._ctx && self._ctx.selector || gsap.utils.toArray)(t)[0] || (typeof t === \"string\" && gsap.config().nullTargetWarn !== false ? console.warn(\"Element not found:\", t) : null);\n},\n    _isWithin = function _isWithin(element, list) {\n  // check if the element is in the list or is a descendant of an element in the list.\n  var i = list.length;\n\n  while (i--) {\n    if (list[i] === element || list[i].contains(element)) {\n      return true;\n    }\n  }\n\n  return false;\n},\n    _getScrollFunc = function _getScrollFunc(element, _ref) {\n  var s = _ref.s,\n      sc = _ref.sc;\n  // we store the scroller functions in an alternating sequenced Array like [element, verticalScrollFunc, horizontalScrollFunc, ...] so that we can minimize memory, maximize performance, and we also record the last position as a \".rec\" property in order to revert to that after refreshing to ensure things don't shift around.\n  _isViewport(element) && (element = _doc.scrollingElement || _docEl);\n\n  var i = _scrollers.indexOf(element),\n      offset = sc === _vertical.sc ? 1 : 2;\n\n  !~i && (i = _scrollers.push(element) - 1);\n  _scrollers[i + offset] || _addListener(element, \"scroll\", _onScroll); // clear the cache when a scroll occurs\n\n  var prev = _scrollers[i + offset],\n      func = prev || (_scrollers[i + offset] = _scrollCacheFunc(_getProxyProp(element, s), true) || (_isViewport(element) ? sc : _scrollCacheFunc(function (value) {\n    return arguments.length ? element[s] = value : element[s];\n  })));\n  func.target = element;\n  prev || (func.smooth = gsap.getProperty(element, \"scrollBehavior\") === \"smooth\"); // only set it the first time (don't reset every time a scrollFunc is requested because perhaps it happens during a refresh() when it's disabled in ScrollTrigger.\n\n  return func;\n},\n    _getVelocityProp = function _getVelocityProp(value, minTimeRefresh, useDelta) {\n  var v1 = value,\n      v2 = value,\n      t1 = _getTime(),\n      t2 = t1,\n      min = minTimeRefresh || 50,\n      dropToZeroTime = Math.max(500, min * 3),\n      update = function update(value, force) {\n    var t = _getTime();\n\n    if (force || t - t1 > min) {\n      v2 = v1;\n      v1 = value;\n      t2 = t1;\n      t1 = t;\n    } else if (useDelta) {\n      v1 += value;\n    } else {\n      // not totally necessary, but makes it a bit more accurate by adjusting the v1 value according to the new slope. This way we're not just ignoring the incoming data. Removing for now because it doesn't seem to make much practical difference and it's probably not worth the kb.\n      v1 = v2 + (value - v2) / (t - t2) * (t1 - t2);\n    }\n  },\n      reset = function reset() {\n    v2 = v1 = useDelta ? 0 : v1;\n    t2 = t1 = 0;\n  },\n      getVelocity = function getVelocity(latestValue) {\n    var tOld = t2,\n        vOld = v2,\n        t = _getTime();\n\n    (latestValue || latestValue === 0) && latestValue !== v1 && update(latestValue);\n    return t1 === t2 || t - t2 > dropToZeroTime ? 0 : (v1 + (useDelta ? vOld : -vOld)) / ((useDelta ? t : t1) - tOld) * 1000;\n  };\n\n  return {\n    update: update,\n    reset: reset,\n    getVelocity: getVelocity\n  };\n},\n    _getEvent = function _getEvent(e, preventDefault) {\n  preventDefault && !e._gsapAllow && e.preventDefault();\n  return e.changedTouches ? e.changedTouches[0] : e;\n},\n    _getAbsoluteMax = function _getAbsoluteMax(a) {\n  var max = Math.max.apply(Math, a),\n      min = Math.min.apply(Math, a);\n  return Math.abs(max) >= Math.abs(min) ? max : min;\n},\n    _setScrollTrigger = function _setScrollTrigger() {\n  ScrollTrigger = gsap.core.globals().ScrollTrigger;\n  ScrollTrigger && ScrollTrigger.core && _integrate();\n},\n    _initCore = function _initCore(core) {\n  gsap = core || _getGSAP();\n\n  if (!_coreInitted && gsap && typeof document !== \"undefined\" && document.body) {\n    _win = window;\n    _doc = document;\n    _docEl = _doc.documentElement;\n    _body = _doc.body;\n    _root = [_win, _doc, _docEl, _body];\n    _clamp = gsap.utils.clamp;\n\n    _context = gsap.core.context || function () {};\n\n    _pointerType = \"onpointerenter\" in _body ? \"pointer\" : \"mouse\"; // isTouch is 0 if no touch, 1 if ONLY touch, and 2 if it can accommodate touch but also other types like mouse/pointer.\n\n    _isTouch = Observer.isTouch = _win.matchMedia && _win.matchMedia(\"(hover: none), (pointer: coarse)\").matches ? 1 : \"ontouchstart\" in _win || navigator.maxTouchPoints > 0 || navigator.msMaxTouchPoints > 0 ? 2 : 0;\n    _eventTypes = Observer.eventTypes = (\"ontouchstart\" in _docEl ? \"touchstart,touchmove,touchcancel,touchend\" : !(\"onpointerdown\" in _docEl) ? \"mousedown,mousemove,mouseup,mouseup\" : \"pointerdown,pointermove,pointercancel,pointerup\").split(\",\");\n    setTimeout(function () {\n      return _startup = 0;\n    }, 500);\n\n    _setScrollTrigger();\n\n    _coreInitted = 1;\n  }\n\n  return _coreInitted;\n};\n\n_horizontal.op = _vertical;\n_scrollers.cache = 0;\nvar Observer = /*#__PURE__*/function () {\n  function Observer(vars) {\n    this.init(vars);\n  }\n\n  var _proto = Observer.prototype;\n\n  _proto.init = function init(vars) {\n    _coreInitted || _initCore(gsap) || console.warn(\"Please gsap.registerPlugin(Observer)\");\n    ScrollTrigger || _setScrollTrigger();\n    var tolerance = vars.tolerance,\n        dragMinimum = vars.dragMinimum,\n        type = vars.type,\n        target = vars.target,\n        lineHeight = vars.lineHeight,\n        debounce = vars.debounce,\n        preventDefault = vars.preventDefault,\n        onStop = vars.onStop,\n        onStopDelay = vars.onStopDelay,\n        ignore = vars.ignore,\n        wheelSpeed = vars.wheelSpeed,\n        event = vars.event,\n        onDragStart = vars.onDragStart,\n        onDragEnd = vars.onDragEnd,\n        onDrag = vars.onDrag,\n        onPress = vars.onPress,\n        onRelease = vars.onRelease,\n        onRight = vars.onRight,\n        onLeft = vars.onLeft,\n        onUp = vars.onUp,\n        onDown = vars.onDown,\n        onChangeX = vars.onChangeX,\n        onChangeY = vars.onChangeY,\n        onChange = vars.onChange,\n        onToggleX = vars.onToggleX,\n        onToggleY = vars.onToggleY,\n        onHover = vars.onHover,\n        onHoverEnd = vars.onHoverEnd,\n        onMove = vars.onMove,\n        ignoreCheck = vars.ignoreCheck,\n        isNormalizer = vars.isNormalizer,\n        onGestureStart = vars.onGestureStart,\n        onGestureEnd = vars.onGestureEnd,\n        onWheel = vars.onWheel,\n        onEnable = vars.onEnable,\n        onDisable = vars.onDisable,\n        onClick = vars.onClick,\n        scrollSpeed = vars.scrollSpeed,\n        capture = vars.capture,\n        allowClicks = vars.allowClicks,\n        lockAxis = vars.lockAxis,\n        onLockAxis = vars.onLockAxis;\n    this.target = target = _getTarget(target) || _docEl;\n    this.vars = vars;\n    ignore && (ignore = gsap.utils.toArray(ignore));\n    tolerance = tolerance || 1e-9;\n    dragMinimum = dragMinimum || 0;\n    wheelSpeed = wheelSpeed || 1;\n    scrollSpeed = scrollSpeed || 1;\n    type = type || \"wheel,touch,pointer\";\n    debounce = debounce !== false;\n    lineHeight || (lineHeight = parseFloat(_win.getComputedStyle(_body).lineHeight) || 22); // note: browser may report \"normal\", so default to 22.\n\n    var id,\n        onStopDelayedCall,\n        dragged,\n        moved,\n        wheeled,\n        locked,\n        axis,\n        self = this,\n        prevDeltaX = 0,\n        prevDeltaY = 0,\n        passive = vars.passive || !preventDefault && vars.passive !== false,\n        scrollFuncX = _getScrollFunc(target, _horizontal),\n        scrollFuncY = _getScrollFunc(target, _vertical),\n        scrollX = scrollFuncX(),\n        scrollY = scrollFuncY(),\n        limitToTouch = ~type.indexOf(\"touch\") && !~type.indexOf(\"pointer\") && _eventTypes[0] === \"pointerdown\",\n        // for devices that accommodate mouse events and touch events, we need to distinguish.\n    isViewport = _isViewport(target),\n        ownerDoc = target.ownerDocument || _doc,\n        deltaX = [0, 0, 0],\n        // wheel, scroll, pointer/touch\n    deltaY = [0, 0, 0],\n        onClickTime = 0,\n        clickCapture = function clickCapture() {\n      return onClickTime = _getTime();\n    },\n        _ignoreCheck = function _ignoreCheck(e, isPointerOrTouch) {\n      return (self.event = e) && ignore && _isWithin(e.target, ignore) || isPointerOrTouch && limitToTouch && e.pointerType !== \"touch\" || ignoreCheck && ignoreCheck(e, isPointerOrTouch);\n    },\n        onStopFunc = function onStopFunc() {\n      self._vx.reset();\n\n      self._vy.reset();\n\n      onStopDelayedCall.pause();\n      onStop && onStop(self);\n    },\n        update = function update() {\n      var dx = self.deltaX = _getAbsoluteMax(deltaX),\n          dy = self.deltaY = _getAbsoluteMax(deltaY),\n          changedX = Math.abs(dx) >= tolerance,\n          changedY = Math.abs(dy) >= tolerance;\n\n      onChange && (changedX || changedY) && onChange(self, dx, dy, deltaX, deltaY); // in ScrollTrigger.normalizeScroll(), we need to know if it was touch/pointer so we need access to the deltaX/deltaY Arrays before we clear them out.\n\n      if (changedX) {\n        onRight && self.deltaX > 0 && onRight(self);\n        onLeft && self.deltaX < 0 && onLeft(self);\n        onChangeX && onChangeX(self);\n        onToggleX && self.deltaX < 0 !== prevDeltaX < 0 && onToggleX(self);\n        prevDeltaX = self.deltaX;\n        deltaX[0] = deltaX[1] = deltaX[2] = 0;\n      }\n\n      if (changedY) {\n        onDown && self.deltaY > 0 && onDown(self);\n        onUp && self.deltaY < 0 && onUp(self);\n        onChangeY && onChangeY(self);\n        onToggleY && self.deltaY < 0 !== prevDeltaY < 0 && onToggleY(self);\n        prevDeltaY = self.deltaY;\n        deltaY[0] = deltaY[1] = deltaY[2] = 0;\n      }\n\n      if (moved || dragged) {\n        onMove && onMove(self);\n\n        if (dragged) {\n          onDragStart && dragged === 1 && onDragStart(self);\n          onDrag && onDrag(self);\n          dragged = 0;\n        }\n\n        moved = false;\n      }\n\n      locked && !(locked = false) && onLockAxis && onLockAxis(self);\n\n      if (wheeled) {\n        onWheel(self);\n        wheeled = false;\n      }\n\n      id = 0;\n    },\n        onDelta = function onDelta(x, y, index) {\n      deltaX[index] += x;\n      deltaY[index] += y;\n\n      self._vx.update(x);\n\n      self._vy.update(y);\n\n      debounce ? id || (id = requestAnimationFrame(update)) : update();\n    },\n        onTouchOrPointerDelta = function onTouchOrPointerDelta(x, y) {\n      if (lockAxis && !axis) {\n        self.axis = axis = Math.abs(x) > Math.abs(y) ? \"x\" : \"y\";\n        locked = true;\n      }\n\n      if (axis !== \"y\") {\n        deltaX[2] += x;\n\n        self._vx.update(x, true); // update the velocity as frequently as possible instead of in the debounced function so that very quick touch-scrolls (flicks) feel natural. If it's the mouse/touch/pointer, force it so that we get snappy/accurate momentum scroll.\n\n      }\n\n      if (axis !== \"x\") {\n        deltaY[2] += y;\n\n        self._vy.update(y, true);\n      }\n\n      debounce ? id || (id = requestAnimationFrame(update)) : update();\n    },\n        _onDrag = function _onDrag(e) {\n      if (_ignoreCheck(e, 1)) {\n        return;\n      }\n\n      e = _getEvent(e, preventDefault);\n      var x = e.clientX,\n          y = e.clientY,\n          dx = x - self.x,\n          dy = y - self.y,\n          isDragging = self.isDragging;\n      self.x = x;\n      self.y = y;\n\n      if (isDragging || (dx || dy) && (Math.abs(self.startX - x) >= dragMinimum || Math.abs(self.startY - y) >= dragMinimum)) {\n        dragged = isDragging ? 2 : 1; // dragged: 0 = not dragging, 1 = first drag, 2 = normal drag\n\n        isDragging || (self.isDragging = true);\n        onTouchOrPointerDelta(dx, dy);\n      }\n    },\n        _onPress = self.onPress = function (e) {\n      if (_ignoreCheck(e, 1) || e && e.button) {\n        return;\n      }\n\n      self.axis = axis = null;\n      onStopDelayedCall.pause();\n      self.isPressed = true;\n      e = _getEvent(e); // note: may need to preventDefault(?) Won't side-scroll on iOS Safari if we do, though.\n\n      prevDeltaX = prevDeltaY = 0;\n      self.startX = self.x = e.clientX;\n      self.startY = self.y = e.clientY;\n\n      self._vx.reset(); // otherwise the t2 may be stale if the user touches and flicks super fast and releases in less than 2 requestAnimationFrame ticks, causing velocity to be 0.\n\n\n      self._vy.reset();\n\n      _addListener(isNormalizer ? target : ownerDoc, _eventTypes[1], _onDrag, passive, true);\n\n      self.deltaX = self.deltaY = 0;\n      onPress && onPress(self);\n    },\n        _onRelease = self.onRelease = function (e) {\n      if (_ignoreCheck(e, 1)) {\n        return;\n      }\n\n      _removeListener(isNormalizer ? target : ownerDoc, _eventTypes[1], _onDrag, true);\n\n      var isTrackingDrag = !isNaN(self.y - self.startY),\n          wasDragging = self.isDragging,\n          isDragNotClick = wasDragging && (Math.abs(self.x - self.startX) > 3 || Math.abs(self.y - self.startY) > 3),\n          // some touch devices need some wiggle room in terms of sensing clicks - the finger may move a few pixels.\n      eventData = _getEvent(e);\n\n      if (!isDragNotClick && isTrackingDrag) {\n        self._vx.reset();\n\n        self._vy.reset(); //if (preventDefault && allowClicks && self.isPressed) { // check isPressed because in a rare edge case, the inputObserver in ScrollTrigger may stopPropagation() on the press/drag, so the onRelease may get fired without the onPress/onDrag ever getting called, thus it could trigger a click to occur on a link after scroll-dragging it.\n\n\n        if (preventDefault && allowClicks) {\n          gsap.delayedCall(0.08, function () {\n            // some browsers (like Firefox) won't trust script-generated clicks, so if the user tries to click on a video to play it, for example, it simply won't work. Since a regular \"click\" event will most likely be generated anyway (one that has its isTrusted flag set to true), we must slightly delay our script-generated click so that the \"real\"/trusted one is prioritized. Remember, when there are duplicate events in quick succession, we suppress all but the first one. Some browsers don't even trigger the \"real\" one at all, so our synthetic one is a safety valve that ensures that no matter what, a click event does get dispatched.\n            if (_getTime() - onClickTime > 300 && !e.defaultPrevented) {\n              if (e.target.click) {\n                //some browsers (like mobile Safari) don't properly trigger the click event\n                e.target.click();\n              } else if (ownerDoc.createEvent) {\n                var syntheticEvent = ownerDoc.createEvent(\"MouseEvents\");\n                syntheticEvent.initMouseEvent(\"click\", true, true, _win, 1, eventData.screenX, eventData.screenY, eventData.clientX, eventData.clientY, false, false, false, false, 0, null);\n                e.target.dispatchEvent(syntheticEvent);\n              }\n            }\n          });\n        }\n      }\n\n      self.isDragging = self.isGesturing = self.isPressed = false;\n      onStop && wasDragging && !isNormalizer && onStopDelayedCall.restart(true);\n      dragged && update(); // in case debouncing, we don't want onDrag to fire AFTER onDragEnd().\n\n      onDragEnd && wasDragging && onDragEnd(self);\n      onRelease && onRelease(self, isDragNotClick);\n    },\n        _onGestureStart = function _onGestureStart(e) {\n      return e.touches && e.touches.length > 1 && (self.isGesturing = true) && onGestureStart(e, self.isDragging);\n    },\n        _onGestureEnd = function _onGestureEnd() {\n      return (self.isGesturing = false) || onGestureEnd(self);\n    },\n        onScroll = function onScroll(e) {\n      if (_ignoreCheck(e)) {\n        return;\n      }\n\n      var x = scrollFuncX(),\n          y = scrollFuncY();\n      onDelta((x - scrollX) * scrollSpeed, (y - scrollY) * scrollSpeed, 1);\n      scrollX = x;\n      scrollY = y;\n      onStop && onStopDelayedCall.restart(true);\n    },\n        _onWheel = function _onWheel(e) {\n      if (_ignoreCheck(e)) {\n        return;\n      }\n\n      e = _getEvent(e, preventDefault);\n      onWheel && (wheeled = true);\n      var multiplier = (e.deltaMode === 1 ? lineHeight : e.deltaMode === 2 ? _win.innerHeight : 1) * wheelSpeed;\n      onDelta(e.deltaX * multiplier, e.deltaY * multiplier, 0);\n      onStop && !isNormalizer && onStopDelayedCall.restart(true);\n    },\n        _onMove = function _onMove(e) {\n      if (_ignoreCheck(e)) {\n        return;\n      }\n\n      var x = e.clientX,\n          y = e.clientY,\n          dx = x - self.x,\n          dy = y - self.y;\n      self.x = x;\n      self.y = y;\n      moved = true;\n      onStop && onStopDelayedCall.restart(true);\n      (dx || dy) && onTouchOrPointerDelta(dx, dy);\n    },\n        _onHover = function _onHover(e) {\n      self.event = e;\n      onHover(self);\n    },\n        _onHoverEnd = function _onHoverEnd(e) {\n      self.event = e;\n      onHoverEnd(self);\n    },\n        _onClick = function _onClick(e) {\n      return _ignoreCheck(e) || _getEvent(e, preventDefault) && onClick(self);\n    };\n\n    onStopDelayedCall = self._dc = gsap.delayedCall(onStopDelay || 0.25, onStopFunc).pause();\n    self.deltaX = self.deltaY = 0;\n    self._vx = _getVelocityProp(0, 50, true);\n    self._vy = _getVelocityProp(0, 50, true);\n    self.scrollX = scrollFuncX;\n    self.scrollY = scrollFuncY;\n    self.isDragging = self.isGesturing = self.isPressed = false;\n\n    _context(this);\n\n    self.enable = function (e) {\n      if (!self.isEnabled) {\n        _addListener(isViewport ? ownerDoc : target, \"scroll\", _onScroll);\n\n        type.indexOf(\"scroll\") >= 0 && _addListener(isViewport ? ownerDoc : target, \"scroll\", onScroll, passive, capture);\n        type.indexOf(\"wheel\") >= 0 && _addListener(target, \"wheel\", _onWheel, passive, capture);\n\n        if (type.indexOf(\"touch\") >= 0 && _isTouch || type.indexOf(\"pointer\") >= 0) {\n          _addListener(target, _eventTypes[0], _onPress, passive, capture);\n\n          _addListener(ownerDoc, _eventTypes[2], _onRelease);\n\n          _addListener(ownerDoc, _eventTypes[3], _onRelease);\n\n          allowClicks && _addListener(target, \"click\", clickCapture, true, true);\n          onClick && _addListener(target, \"click\", _onClick);\n          onGestureStart && _addListener(ownerDoc, \"gesturestart\", _onGestureStart);\n          onGestureEnd && _addListener(ownerDoc, \"gestureend\", _onGestureEnd);\n          onHover && _addListener(target, _pointerType + \"enter\", _onHover);\n          onHoverEnd && _addListener(target, _pointerType + \"leave\", _onHoverEnd);\n          onMove && _addListener(target, _pointerType + \"move\", _onMove);\n        }\n\n        self.isEnabled = true;\n        self.isDragging = self.isGesturing = self.isPressed = moved = dragged = false;\n\n        self._vx.reset();\n\n        self._vy.reset();\n\n        scrollX = scrollFuncX();\n        scrollY = scrollFuncY();\n        e && e.type && _onPress(e);\n        onEnable && onEnable(self);\n      }\n\n      return self;\n    };\n\n    self.disable = function () {\n      if (self.isEnabled) {\n        // only remove the _onScroll listener if there aren't any others that rely on the functionality.\n        _observers.filter(function (o) {\n          return o !== self && _isViewport(o.target);\n        }).length || _removeListener(isViewport ? ownerDoc : target, \"scroll\", _onScroll);\n\n        if (self.isPressed) {\n          self._vx.reset();\n\n          self._vy.reset();\n\n          _removeListener(isNormalizer ? target : ownerDoc, _eventTypes[1], _onDrag, true);\n        }\n\n        _removeListener(isViewport ? ownerDoc : target, \"scroll\", onScroll, capture);\n\n        _removeListener(target, \"wheel\", _onWheel, capture);\n\n        _removeListener(target, _eventTypes[0], _onPress, capture);\n\n        _removeListener(ownerDoc, _eventTypes[2], _onRelease);\n\n        _removeListener(ownerDoc, _eventTypes[3], _onRelease);\n\n        _removeListener(target, \"click\", clickCapture, true);\n\n        _removeListener(target, \"click\", _onClick);\n\n        _removeListener(ownerDoc, \"gesturestart\", _onGestureStart);\n\n        _removeListener(ownerDoc, \"gestureend\", _onGestureEnd);\n\n        _removeListener(target, _pointerType + \"enter\", _onHover);\n\n        _removeListener(target, _pointerType + \"leave\", _onHoverEnd);\n\n        _removeListener(target, _pointerType + \"move\", _onMove);\n\n        self.isEnabled = self.isPressed = self.isDragging = false;\n        onDisable && onDisable(self);\n      }\n    };\n\n    self.kill = self.revert = function () {\n      self.disable();\n\n      var i = _observers.indexOf(self);\n\n      i >= 0 && _observers.splice(i, 1);\n      _normalizer === self && (_normalizer = 0);\n    };\n\n    _observers.push(self);\n\n    isNormalizer && _isViewport(target) && (_normalizer = self);\n    self.enable(event);\n  };\n\n  _createClass(Observer, [{\n    key: \"velocityX\",\n    get: function get() {\n      return this._vx.getVelocity();\n    }\n  }, {\n    key: \"velocityY\",\n    get: function get() {\n      return this._vy.getVelocity();\n    }\n  }]);\n\n  return Observer;\n}();\nObserver.version = \"3.13.0\";\n\nObserver.create = function (vars) {\n  return new Observer(vars);\n};\n\nObserver.register = _initCore;\n\nObserver.getAll = function () {\n  return _observers.slice();\n};\n\nObserver.getById = function (id) {\n  return _observers.filter(function (o) {\n    return o.vars.id === id;\n  })[0];\n};\n\n_getGSAP() && gsap.registerPlugin(Observer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/gsap/Observer.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/gsap/ScrollTrigger.js":
/*!********************************************!*\
  !*** ./node_modules/gsap/ScrollTrigger.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ScrollTrigger: () => (/* binding */ ScrollTrigger),\n/* harmony export */   \"default\": () => (/* binding */ ScrollTrigger)\n/* harmony export */ });\n/* harmony import */ var _Observer_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Observer.js */ \"(app-pages-browser)/./node_modules/gsap/Observer.js\");\n/*!\n * ScrollTrigger 3.13.0\n * https://gsap.com\n *\n * @license Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: Jack Doyle, <EMAIL>\n*/\n\n/* eslint-disable */\n\n\nvar gsap,\n    _coreInitted,\n    _win,\n    _doc,\n    _docEl,\n    _body,\n    _root,\n    _resizeDelay,\n    _toArray,\n    _clamp,\n    _time2,\n    _syncInterval,\n    _refreshing,\n    _pointerIsDown,\n    _transformProp,\n    _i,\n    _prevWidth,\n    _prevHeight,\n    _autoRefresh,\n    _sort,\n    _suppressOverwrites,\n    _ignoreResize,\n    _normalizer,\n    _ignoreMobileResize,\n    _baseScreenHeight,\n    _baseScreenWidth,\n    _fixIOSBug,\n    _context,\n    _scrollRestoration,\n    _div100vh,\n    _100vh,\n    _isReverted,\n    _clampingMax,\n    _limitCallbacks,\n    // if true, we'll only trigger callbacks if the active state toggles, so if you scroll immediately past both the start and end positions of a ScrollTrigger (thus inactive to inactive), neither its onEnter nor onLeave will be called. This is useful during startup.\n_startup = 1,\n    _getTime = Date.now,\n    _time1 = _getTime(),\n    _lastScrollTime = 0,\n    _enabled = 0,\n    _parseClamp = function _parseClamp(value, type, self) {\n  var clamp = _isString(value) && (value.substr(0, 6) === \"clamp(\" || value.indexOf(\"max\") > -1);\n  self[\"_\" + type + \"Clamp\"] = clamp;\n  return clamp ? value.substr(6, value.length - 7) : value;\n},\n    _keepClamp = function _keepClamp(value, clamp) {\n  return clamp && (!_isString(value) || value.substr(0, 6) !== \"clamp(\") ? \"clamp(\" + value + \")\" : value;\n},\n    _rafBugFix = function _rafBugFix() {\n  return _enabled && requestAnimationFrame(_rafBugFix);\n},\n    // in some browsers (like Firefox), screen repaints weren't consistent unless we had SOMETHING queued up in requestAnimationFrame()! So this just creates a super simple loop to keep it alive and smooth out repaints.\n_pointerDownHandler = function _pointerDownHandler() {\n  return _pointerIsDown = 1;\n},\n    _pointerUpHandler = function _pointerUpHandler() {\n  return _pointerIsDown = 0;\n},\n    _passThrough = function _passThrough(v) {\n  return v;\n},\n    _round = function _round(value) {\n  return Math.round(value * 100000) / 100000 || 0;\n},\n    _windowExists = function _windowExists() {\n  return typeof window !== \"undefined\";\n},\n    _getGSAP = function _getGSAP() {\n  return gsap || _windowExists() && (gsap = window.gsap) && gsap.registerPlugin && gsap;\n},\n    _isViewport = function _isViewport(e) {\n  return !!~_root.indexOf(e);\n},\n    _getViewportDimension = function _getViewportDimension(dimensionProperty) {\n  return (dimensionProperty === \"Height\" ? _100vh : _win[\"inner\" + dimensionProperty]) || _docEl[\"client\" + dimensionProperty] || _body[\"client\" + dimensionProperty];\n},\n    _getBoundsFunc = function _getBoundsFunc(element) {\n  return (0,_Observer_js__WEBPACK_IMPORTED_MODULE_0__._getProxyProp)(element, \"getBoundingClientRect\") || (_isViewport(element) ? function () {\n    _winOffsets.width = _win.innerWidth;\n    _winOffsets.height = _100vh;\n    return _winOffsets;\n  } : function () {\n    return _getBounds(element);\n  });\n},\n    _getSizeFunc = function _getSizeFunc(scroller, isViewport, _ref) {\n  var d = _ref.d,\n      d2 = _ref.d2,\n      a = _ref.a;\n  return (a = (0,_Observer_js__WEBPACK_IMPORTED_MODULE_0__._getProxyProp)(scroller, \"getBoundingClientRect\")) ? function () {\n    return a()[d];\n  } : function () {\n    return (isViewport ? _getViewportDimension(d2) : scroller[\"client\" + d2]) || 0;\n  };\n},\n    _getOffsetsFunc = function _getOffsetsFunc(element, isViewport) {\n  return !isViewport || ~_Observer_js__WEBPACK_IMPORTED_MODULE_0__._proxies.indexOf(element) ? _getBoundsFunc(element) : function () {\n    return _winOffsets;\n  };\n},\n    _maxScroll = function _maxScroll(element, _ref2) {\n  var s = _ref2.s,\n      d2 = _ref2.d2,\n      d = _ref2.d,\n      a = _ref2.a;\n  return Math.max(0, (s = \"scroll\" + d2) && (a = (0,_Observer_js__WEBPACK_IMPORTED_MODULE_0__._getProxyProp)(element, s)) ? a() - _getBoundsFunc(element)()[d] : _isViewport(element) ? (_docEl[s] || _body[s]) - _getViewportDimension(d2) : element[s] - element[\"offset\" + d2]);\n},\n    _iterateAutoRefresh = function _iterateAutoRefresh(func, events) {\n  for (var i = 0; i < _autoRefresh.length; i += 3) {\n    (!events || ~events.indexOf(_autoRefresh[i + 1])) && func(_autoRefresh[i], _autoRefresh[i + 1], _autoRefresh[i + 2]);\n  }\n},\n    _isString = function _isString(value) {\n  return typeof value === \"string\";\n},\n    _isFunction = function _isFunction(value) {\n  return typeof value === \"function\";\n},\n    _isNumber = function _isNumber(value) {\n  return typeof value === \"number\";\n},\n    _isObject = function _isObject(value) {\n  return typeof value === \"object\";\n},\n    _endAnimation = function _endAnimation(animation, reversed, pause) {\n  return animation && animation.progress(reversed ? 0 : 1) && pause && animation.pause();\n},\n    _callback = function _callback(self, func) {\n  if (self.enabled) {\n    var result = self._ctx ? self._ctx.add(function () {\n      return func(self);\n    }) : func(self);\n    result && result.totalTime && (self.callbackAnimation = result);\n  }\n},\n    _abs = Math.abs,\n    _left = \"left\",\n    _top = \"top\",\n    _right = \"right\",\n    _bottom = \"bottom\",\n    _width = \"width\",\n    _height = \"height\",\n    _Right = \"Right\",\n    _Left = \"Left\",\n    _Top = \"Top\",\n    _Bottom = \"Bottom\",\n    _padding = \"padding\",\n    _margin = \"margin\",\n    _Width = \"Width\",\n    _Height = \"Height\",\n    _px = \"px\",\n    _getComputedStyle = function _getComputedStyle(element) {\n  return _win.getComputedStyle(element);\n},\n    _makePositionable = function _makePositionable(element) {\n  // if the element already has position: absolute or fixed, leave that, otherwise make it position: relative\n  var position = _getComputedStyle(element).position;\n\n  element.style.position = position === \"absolute\" || position === \"fixed\" ? position : \"relative\";\n},\n    _setDefaults = function _setDefaults(obj, defaults) {\n  for (var p in defaults) {\n    p in obj || (obj[p] = defaults[p]);\n  }\n\n  return obj;\n},\n    _getBounds = function _getBounds(element, withoutTransforms) {\n  var tween = withoutTransforms && _getComputedStyle(element)[_transformProp] !== \"matrix(1, 0, 0, 1, 0, 0)\" && gsap.to(element, {\n    x: 0,\n    y: 0,\n    xPercent: 0,\n    yPercent: 0,\n    rotation: 0,\n    rotationX: 0,\n    rotationY: 0,\n    scale: 1,\n    skewX: 0,\n    skewY: 0\n  }).progress(1),\n      bounds = element.getBoundingClientRect();\n  tween && tween.progress(0).kill();\n  return bounds;\n},\n    _getSize = function _getSize(element, _ref3) {\n  var d2 = _ref3.d2;\n  return element[\"offset\" + d2] || element[\"client\" + d2] || 0;\n},\n    _getLabelRatioArray = function _getLabelRatioArray(timeline) {\n  var a = [],\n      labels = timeline.labels,\n      duration = timeline.duration(),\n      p;\n\n  for (p in labels) {\n    a.push(labels[p] / duration);\n  }\n\n  return a;\n},\n    _getClosestLabel = function _getClosestLabel(animation) {\n  return function (value) {\n    return gsap.utils.snap(_getLabelRatioArray(animation), value);\n  };\n},\n    _snapDirectional = function _snapDirectional(snapIncrementOrArray) {\n  var snap = gsap.utils.snap(snapIncrementOrArray),\n      a = Array.isArray(snapIncrementOrArray) && snapIncrementOrArray.slice(0).sort(function (a, b) {\n    return a - b;\n  });\n  return a ? function (value, direction, threshold) {\n    if (threshold === void 0) {\n      threshold = 1e-3;\n    }\n\n    var i;\n\n    if (!direction) {\n      return snap(value);\n    }\n\n    if (direction > 0) {\n      value -= threshold; // to avoid rounding errors. If we're too strict, it might snap forward, then immediately again, and again.\n\n      for (i = 0; i < a.length; i++) {\n        if (a[i] >= value) {\n          return a[i];\n        }\n      }\n\n      return a[i - 1];\n    } else {\n      i = a.length;\n      value += threshold;\n\n      while (i--) {\n        if (a[i] <= value) {\n          return a[i];\n        }\n      }\n    }\n\n    return a[0];\n  } : function (value, direction, threshold) {\n    if (threshold === void 0) {\n      threshold = 1e-3;\n    }\n\n    var snapped = snap(value);\n    return !direction || Math.abs(snapped - value) < threshold || snapped - value < 0 === direction < 0 ? snapped : snap(direction < 0 ? value - snapIncrementOrArray : value + snapIncrementOrArray);\n  };\n},\n    _getLabelAtDirection = function _getLabelAtDirection(timeline) {\n  return function (value, st) {\n    return _snapDirectional(_getLabelRatioArray(timeline))(value, st.direction);\n  };\n},\n    _multiListener = function _multiListener(func, element, types, callback) {\n  return types.split(\",\").forEach(function (type) {\n    return func(element, type, callback);\n  });\n},\n    _addListener = function _addListener(element, type, func, nonPassive, capture) {\n  return element.addEventListener(type, func, {\n    passive: !nonPassive,\n    capture: !!capture\n  });\n},\n    _removeListener = function _removeListener(element, type, func, capture) {\n  return element.removeEventListener(type, func, !!capture);\n},\n    _wheelListener = function _wheelListener(func, el, scrollFunc) {\n  scrollFunc = scrollFunc && scrollFunc.wheelHandler;\n\n  if (scrollFunc) {\n    func(el, \"wheel\", scrollFunc);\n    func(el, \"touchmove\", scrollFunc);\n  }\n},\n    _markerDefaults = {\n  startColor: \"green\",\n  endColor: \"red\",\n  indent: 0,\n  fontSize: \"16px\",\n  fontWeight: \"normal\"\n},\n    _defaults = {\n  toggleActions: \"play\",\n  anticipatePin: 0\n},\n    _keywords = {\n  top: 0,\n  left: 0,\n  center: 0.5,\n  bottom: 1,\n  right: 1\n},\n    _offsetToPx = function _offsetToPx(value, size) {\n  if (_isString(value)) {\n    var eqIndex = value.indexOf(\"=\"),\n        relative = ~eqIndex ? +(value.charAt(eqIndex - 1) + 1) * parseFloat(value.substr(eqIndex + 1)) : 0;\n\n    if (~eqIndex) {\n      value.indexOf(\"%\") > eqIndex && (relative *= size / 100);\n      value = value.substr(0, eqIndex - 1);\n    }\n\n    value = relative + (value in _keywords ? _keywords[value] * size : ~value.indexOf(\"%\") ? parseFloat(value) * size / 100 : parseFloat(value) || 0);\n  }\n\n  return value;\n},\n    _createMarker = function _createMarker(type, name, container, direction, _ref4, offset, matchWidthEl, containerAnimation) {\n  var startColor = _ref4.startColor,\n      endColor = _ref4.endColor,\n      fontSize = _ref4.fontSize,\n      indent = _ref4.indent,\n      fontWeight = _ref4.fontWeight;\n\n  var e = _doc.createElement(\"div\"),\n      useFixedPosition = _isViewport(container) || (0,_Observer_js__WEBPACK_IMPORTED_MODULE_0__._getProxyProp)(container, \"pinType\") === \"fixed\",\n      isScroller = type.indexOf(\"scroller\") !== -1,\n      parent = useFixedPosition ? _body : container,\n      isStart = type.indexOf(\"start\") !== -1,\n      color = isStart ? startColor : endColor,\n      css = \"border-color:\" + color + \";font-size:\" + fontSize + \";color:\" + color + \";font-weight:\" + fontWeight + \";pointer-events:none;white-space:nowrap;font-family:sans-serif,Arial;z-index:1000;padding:4px 8px;border-width:0;border-style:solid;\";\n\n  css += \"position:\" + ((isScroller || containerAnimation) && useFixedPosition ? \"fixed;\" : \"absolute;\");\n  (isScroller || containerAnimation || !useFixedPosition) && (css += (direction === _Observer_js__WEBPACK_IMPORTED_MODULE_0__._vertical ? _right : _bottom) + \":\" + (offset + parseFloat(indent)) + \"px;\");\n  matchWidthEl && (css += \"box-sizing:border-box;text-align:left;width:\" + matchWidthEl.offsetWidth + \"px;\");\n  e._isStart = isStart;\n  e.setAttribute(\"class\", \"gsap-marker-\" + type + (name ? \" marker-\" + name : \"\"));\n  e.style.cssText = css;\n  e.innerText = name || name === 0 ? type + \"-\" + name : type;\n  parent.children[0] ? parent.insertBefore(e, parent.children[0]) : parent.appendChild(e);\n  e._offset = e[\"offset\" + direction.op.d2];\n\n  _positionMarker(e, 0, direction, isStart);\n\n  return e;\n},\n    _positionMarker = function _positionMarker(marker, start, direction, flipped) {\n  var vars = {\n    display: \"block\"\n  },\n      side = direction[flipped ? \"os2\" : \"p2\"],\n      oppositeSide = direction[flipped ? \"p2\" : \"os2\"];\n  marker._isFlipped = flipped;\n  vars[direction.a + \"Percent\"] = flipped ? -100 : 0;\n  vars[direction.a] = flipped ? \"1px\" : 0;\n  vars[\"border\" + side + _Width] = 1;\n  vars[\"border\" + oppositeSide + _Width] = 0;\n  vars[direction.p] = start + \"px\";\n  gsap.set(marker, vars);\n},\n    _triggers = [],\n    _ids = {},\n    _rafID,\n    _sync = function _sync() {\n  return _getTime() - _lastScrollTime > 34 && (_rafID || (_rafID = requestAnimationFrame(_updateAll)));\n},\n    _onScroll = function _onScroll() {\n  // previously, we tried to optimize performance by batching/deferring to the next requestAnimationFrame(), but discovered that Safari has a few bugs that make this unworkable (especially on iOS). See https://codepen.io/GreenSock/pen/16c435b12ef09c38125204818e7b45fc?editors=0010 and https://codepen.io/GreenSock/pen/JjOxYpQ/3dd65ccec5a60f1d862c355d84d14562?editors=0010 and https://codepen.io/GreenSock/pen/ExbrPNa/087cef197dc35445a0951e8935c41503?editors=0010\n  if (!_normalizer || !_normalizer.isPressed || _normalizer.startX > _body.clientWidth) {\n    // if the user is dragging the scrollbar, allow it.\n    _Observer_js__WEBPACK_IMPORTED_MODULE_0__._scrollers.cache++;\n\n    if (_normalizer) {\n      _rafID || (_rafID = requestAnimationFrame(_updateAll));\n    } else {\n      _updateAll(); // Safari in particular (on desktop) NEEDS the immediate update rather than waiting for a requestAnimationFrame() whereas iOS seems to benefit from waiting for the requestAnimationFrame() tick, at least when normalizing. See https://codepen.io/GreenSock/pen/qBYozqO?editors=0110\n\n    }\n\n    _lastScrollTime || _dispatch(\"scrollStart\");\n    _lastScrollTime = _getTime();\n  }\n},\n    _setBaseDimensions = function _setBaseDimensions() {\n  _baseScreenWidth = _win.innerWidth;\n  _baseScreenHeight = _win.innerHeight;\n},\n    _onResize = function _onResize(force) {\n  _Observer_js__WEBPACK_IMPORTED_MODULE_0__._scrollers.cache++;\n  (force === true || !_refreshing && !_ignoreResize && !_doc.fullscreenElement && !_doc.webkitFullscreenElement && (!_ignoreMobileResize || _baseScreenWidth !== _win.innerWidth || Math.abs(_win.innerHeight - _baseScreenHeight) > _win.innerHeight * 0.25)) && _resizeDelay.restart(true);\n},\n    // ignore resizes triggered by refresh()\n_listeners = {},\n    _emptyArray = [],\n    _softRefresh = function _softRefresh() {\n  return _removeListener(ScrollTrigger, \"scrollEnd\", _softRefresh) || _refreshAll(true);\n},\n    _dispatch = function _dispatch(type) {\n  return _listeners[type] && _listeners[type].map(function (f) {\n    return f();\n  }) || _emptyArray;\n},\n    _savedStyles = [],\n    // when ScrollTrigger.saveStyles() is called, the inline styles are recorded in this Array in a sequential format like [element, cssText, gsCache, media]. This keeps it very memory-efficient and fast to iterate through.\n_revertRecorded = function _revertRecorded(media) {\n  for (var i = 0; i < _savedStyles.length; i += 5) {\n    if (!media || _savedStyles[i + 4] && _savedStyles[i + 4].query === media) {\n      _savedStyles[i].style.cssText = _savedStyles[i + 1];\n      _savedStyles[i].getBBox && _savedStyles[i].setAttribute(\"transform\", _savedStyles[i + 2] || \"\");\n      _savedStyles[i + 3].uncache = 1;\n    }\n  }\n},\n    _revertAll = function _revertAll(kill, media) {\n  var trigger;\n\n  for (_i = 0; _i < _triggers.length; _i++) {\n    trigger = _triggers[_i];\n\n    if (trigger && (!media || trigger._ctx === media)) {\n      if (kill) {\n        trigger.kill(1);\n      } else {\n        trigger.revert(true, true);\n      }\n    }\n  }\n\n  _isReverted = true;\n  media && _revertRecorded(media);\n  media || _dispatch(\"revert\");\n},\n    _clearScrollMemory = function _clearScrollMemory(scrollRestoration, force) {\n  // zero-out all the recorded scroll positions. Don't use _triggers because if, for example, .matchMedia() is used to create some ScrollTriggers and then the user resizes and it removes ALL ScrollTriggers, and then go back to a size where there are ScrollTriggers, it would have kept the position(s) saved from the initial state.\n  _Observer_js__WEBPACK_IMPORTED_MODULE_0__._scrollers.cache++;\n  (force || !_refreshingAll) && _Observer_js__WEBPACK_IMPORTED_MODULE_0__._scrollers.forEach(function (obj) {\n    return _isFunction(obj) && obj.cacheID++ && (obj.rec = 0);\n  });\n  _isString(scrollRestoration) && (_win.history.scrollRestoration = _scrollRestoration = scrollRestoration);\n},\n    _refreshingAll,\n    _refreshID = 0,\n    _queueRefreshID,\n    _queueRefreshAll = function _queueRefreshAll() {\n  // we don't want to call _refreshAll() every time we create a new ScrollTrigger (for performance reasons) - it's better to batch them. Some frameworks dynamically load content and we can't rely on the window's \"load\" or \"DOMContentLoaded\" events to trigger it.\n  if (_queueRefreshID !== _refreshID) {\n    var id = _queueRefreshID = _refreshID;\n    requestAnimationFrame(function () {\n      return id === _refreshID && _refreshAll(true);\n    });\n  }\n},\n    _refresh100vh = function _refresh100vh() {\n  _body.appendChild(_div100vh);\n\n  _100vh = !_normalizer && _div100vh.offsetHeight || _win.innerHeight;\n\n  _body.removeChild(_div100vh);\n},\n    _hideAllMarkers = function _hideAllMarkers(hide) {\n  return _toArray(\".gsap-marker-start, .gsap-marker-end, .gsap-marker-scroller-start, .gsap-marker-scroller-end\").forEach(function (el) {\n    return el.style.display = hide ? \"none\" : \"block\";\n  });\n},\n    _refreshAll = function _refreshAll(force, skipRevert) {\n  _docEl = _doc.documentElement; // some frameworks like Astro may cache the <body> and replace it during routing, so we'll just re-record the _docEl and _body for safety (otherwise, the markers may not get added properly).\n\n  _body = _doc.body;\n  _root = [_win, _doc, _docEl, _body];\n\n  if (_lastScrollTime && !force && !_isReverted) {\n    _addListener(ScrollTrigger, \"scrollEnd\", _softRefresh);\n\n    return;\n  }\n\n  _refresh100vh();\n\n  _refreshingAll = ScrollTrigger.isRefreshing = true;\n\n  _Observer_js__WEBPACK_IMPORTED_MODULE_0__._scrollers.forEach(function (obj) {\n    return _isFunction(obj) && ++obj.cacheID && (obj.rec = obj());\n  }); // force the clearing of the cache because some browsers take a little while to dispatch the \"scroll\" event and the user may have changed the scroll position and then called ScrollTrigger.refresh() right away\n\n\n  var refreshInits = _dispatch(\"refreshInit\");\n\n  _sort && ScrollTrigger.sort();\n  skipRevert || _revertAll();\n\n  _Observer_js__WEBPACK_IMPORTED_MODULE_0__._scrollers.forEach(function (obj) {\n    if (_isFunction(obj)) {\n      obj.smooth && (obj.target.style.scrollBehavior = \"auto\"); // smooth scrolling interferes\n\n      obj(0);\n    }\n  });\n\n  _triggers.slice(0).forEach(function (t) {\n    return t.refresh();\n  }); // don't loop with _i because during a refresh() someone could call ScrollTrigger.update() which would iterate through _i resulting in a skip.\n\n\n  _isReverted = false;\n\n  _triggers.forEach(function (t) {\n    // nested pins (pinnedContainer) with pinSpacing may expand the container, so we must accommodate that here.\n    if (t._subPinOffset && t.pin) {\n      var prop = t.vars.horizontal ? \"offsetWidth\" : \"offsetHeight\",\n          original = t.pin[prop];\n      t.revert(true, 1);\n      t.adjustPinSpacing(t.pin[prop] - original);\n      t.refresh();\n    }\n  });\n\n  _clampingMax = 1; // pinSpacing might be propping a page open, thus when we .setPositions() to clamp a ScrollTrigger's end we should leave the pinSpacing alone. That's what this flag is for.\n\n  _hideAllMarkers(true);\n\n  _triggers.forEach(function (t) {\n    // the scroller's max scroll position may change after all the ScrollTriggers refreshed (like pinning could push it down), so we need to loop back and correct any with end: \"max\". Same for anything with a clamped end\n    var max = _maxScroll(t.scroller, t._dir),\n        endClamp = t.vars.end === \"max\" || t._endClamp && t.end > max,\n        startClamp = t._startClamp && t.start >= max;\n\n    (endClamp || startClamp) && t.setPositions(startClamp ? max - 1 : t.start, endClamp ? Math.max(startClamp ? max : t.start + 1, max) : t.end, true);\n  });\n\n  _hideAllMarkers(false);\n\n  _clampingMax = 0;\n  refreshInits.forEach(function (result) {\n    return result && result.render && result.render(-1);\n  }); // if the onRefreshInit() returns an animation (typically a gsap.set()), revert it. This makes it easy to put things in a certain spot before refreshing for measurement purposes, and then put things back.\n\n  _Observer_js__WEBPACK_IMPORTED_MODULE_0__._scrollers.forEach(function (obj) {\n    if (_isFunction(obj)) {\n      obj.smooth && requestAnimationFrame(function () {\n        return obj.target.style.scrollBehavior = \"smooth\";\n      });\n      obj.rec && obj(obj.rec);\n    }\n  });\n\n  _clearScrollMemory(_scrollRestoration, 1);\n\n  _resizeDelay.pause();\n\n  _refreshID++;\n  _refreshingAll = 2;\n\n  _updateAll(2);\n\n  _triggers.forEach(function (t) {\n    return _isFunction(t.vars.onRefresh) && t.vars.onRefresh(t);\n  });\n\n  _refreshingAll = ScrollTrigger.isRefreshing = false;\n\n  _dispatch(\"refresh\");\n},\n    _lastScroll = 0,\n    _direction = 1,\n    _primary,\n    _updateAll = function _updateAll(force) {\n  if (force === 2 || !_refreshingAll && !_isReverted) {\n    // _isReverted could be true if, for example, a matchMedia() is in the process of executing. We don't want to update during the time everything is reverted.\n    ScrollTrigger.isUpdating = true;\n    _primary && _primary.update(0); // ScrollSmoother uses refreshPriority -9999 to become the primary that gets updated before all others because it affects the scroll position.\n\n    var l = _triggers.length,\n        time = _getTime(),\n        recordVelocity = time - _time1 >= 50,\n        scroll = l && _triggers[0].scroll();\n\n    _direction = _lastScroll > scroll ? -1 : 1;\n    _refreshingAll || (_lastScroll = scroll);\n\n    if (recordVelocity) {\n      if (_lastScrollTime && !_pointerIsDown && time - _lastScrollTime > 200) {\n        _lastScrollTime = 0;\n\n        _dispatch(\"scrollEnd\");\n      }\n\n      _time2 = _time1;\n      _time1 = time;\n    }\n\n    if (_direction < 0) {\n      _i = l;\n\n      while (_i-- > 0) {\n        _triggers[_i] && _triggers[_i].update(0, recordVelocity);\n      }\n\n      _direction = 1;\n    } else {\n      for (_i = 0; _i < l; _i++) {\n        _triggers[_i] && _triggers[_i].update(0, recordVelocity);\n      }\n    }\n\n    ScrollTrigger.isUpdating = false;\n  }\n\n  _rafID = 0;\n},\n    _propNamesToCopy = [_left, _top, _bottom, _right, _margin + _Bottom, _margin + _Right, _margin + _Top, _margin + _Left, \"display\", \"flexShrink\", \"float\", \"zIndex\", \"gridColumnStart\", \"gridColumnEnd\", \"gridRowStart\", \"gridRowEnd\", \"gridArea\", \"justifySelf\", \"alignSelf\", \"placeSelf\", \"order\"],\n    _stateProps = _propNamesToCopy.concat([_width, _height, \"boxSizing\", \"max\" + _Width, \"max\" + _Height, \"position\", _margin, _padding, _padding + _Top, _padding + _Right, _padding + _Bottom, _padding + _Left]),\n    _swapPinOut = function _swapPinOut(pin, spacer, state) {\n  _setState(state);\n\n  var cache = pin._gsap;\n\n  if (cache.spacerIsNative) {\n    _setState(cache.spacerState);\n  } else if (pin._gsap.swappedIn) {\n    var parent = spacer.parentNode;\n\n    if (parent) {\n      parent.insertBefore(pin, spacer);\n      parent.removeChild(spacer);\n    }\n  }\n\n  pin._gsap.swappedIn = false;\n},\n    _swapPinIn = function _swapPinIn(pin, spacer, cs, spacerState) {\n  if (!pin._gsap.swappedIn) {\n    var i = _propNamesToCopy.length,\n        spacerStyle = spacer.style,\n        pinStyle = pin.style,\n        p;\n\n    while (i--) {\n      p = _propNamesToCopy[i];\n      spacerStyle[p] = cs[p];\n    }\n\n    spacerStyle.position = cs.position === \"absolute\" ? \"absolute\" : \"relative\";\n    cs.display === \"inline\" && (spacerStyle.display = \"inline-block\");\n    pinStyle[_bottom] = pinStyle[_right] = \"auto\";\n    spacerStyle.flexBasis = cs.flexBasis || \"auto\";\n    spacerStyle.overflow = \"visible\";\n    spacerStyle.boxSizing = \"border-box\";\n    spacerStyle[_width] = _getSize(pin, _Observer_js__WEBPACK_IMPORTED_MODULE_0__._horizontal) + _px;\n    spacerStyle[_height] = _getSize(pin, _Observer_js__WEBPACK_IMPORTED_MODULE_0__._vertical) + _px;\n    spacerStyle[_padding] = pinStyle[_margin] = pinStyle[_top] = pinStyle[_left] = \"0\";\n\n    _setState(spacerState);\n\n    pinStyle[_width] = pinStyle[\"max\" + _Width] = cs[_width];\n    pinStyle[_height] = pinStyle[\"max\" + _Height] = cs[_height];\n    pinStyle[_padding] = cs[_padding];\n\n    if (pin.parentNode !== spacer) {\n      pin.parentNode.insertBefore(spacer, pin);\n      spacer.appendChild(pin);\n    }\n\n    pin._gsap.swappedIn = true;\n  }\n},\n    _capsExp = /([A-Z])/g,\n    _setState = function _setState(state) {\n  if (state) {\n    var style = state.t.style,\n        l = state.length,\n        i = 0,\n        p,\n        value;\n    (state.t._gsap || gsap.core.getCache(state.t)).uncache = 1; // otherwise transforms may be off\n\n    for (; i < l; i += 2) {\n      value = state[i + 1];\n      p = state[i];\n\n      if (value) {\n        style[p] = value;\n      } else if (style[p]) {\n        style.removeProperty(p.replace(_capsExp, \"-$1\").toLowerCase());\n      }\n    }\n  }\n},\n    _getState = function _getState(element) {\n  // returns an Array with alternating values like [property, value, property, value] and a \"t\" property pointing to the target (element). Makes it fast and cheap.\n  var l = _stateProps.length,\n      style = element.style,\n      state = [],\n      i = 0;\n\n  for (; i < l; i++) {\n    state.push(_stateProps[i], style[_stateProps[i]]);\n  }\n\n  state.t = element;\n  return state;\n},\n    _copyState = function _copyState(state, override, omitOffsets) {\n  var result = [],\n      l = state.length,\n      i = omitOffsets ? 8 : 0,\n      // skip top, left, right, bottom if omitOffsets is true\n  p;\n\n  for (; i < l; i += 2) {\n    p = state[i];\n    result.push(p, p in override ? override[p] : state[i + 1]);\n  }\n\n  result.t = state.t;\n  return result;\n},\n    _winOffsets = {\n  left: 0,\n  top: 0\n},\n    // // potential future feature (?) Allow users to calculate where a trigger hits (scroll position) like getScrollPosition(\"#id\", \"top bottom\")\n// _getScrollPosition = (trigger, position, {scroller, containerAnimation, horizontal}) => {\n// \tscroller = _getTarget(scroller || _win);\n// \tlet direction = horizontal ? _horizontal : _vertical,\n// \t\tisViewport = _isViewport(scroller);\n// \t_getSizeFunc(scroller, isViewport, direction);\n// \treturn _parsePosition(position, _getTarget(trigger), _getSizeFunc(scroller, isViewport, direction)(), direction, _getScrollFunc(scroller, direction)(), 0, 0, 0, _getOffsetsFunc(scroller, isViewport)(), isViewport ? 0 : parseFloat(_getComputedStyle(scroller)[\"border\" + direction.p2 + _Width]) || 0, 0, containerAnimation ? containerAnimation.duration() : _maxScroll(scroller), containerAnimation);\n// },\n_parsePosition = function _parsePosition(value, trigger, scrollerSize, direction, scroll, marker, markerScroller, self, scrollerBounds, borderWidth, useFixedPosition, scrollerMax, containerAnimation, clampZeroProp) {\n  _isFunction(value) && (value = value(self));\n\n  if (_isString(value) && value.substr(0, 3) === \"max\") {\n    value = scrollerMax + (value.charAt(4) === \"=\" ? _offsetToPx(\"0\" + value.substr(3), scrollerSize) : 0);\n  }\n\n  var time = containerAnimation ? containerAnimation.time() : 0,\n      p1,\n      p2,\n      element;\n  containerAnimation && containerAnimation.seek(0);\n  isNaN(value) || (value = +value); // convert a string number like \"45\" to an actual number\n\n  if (!_isNumber(value)) {\n    _isFunction(trigger) && (trigger = trigger(self));\n    var offsets = (value || \"0\").split(\" \"),\n        bounds,\n        localOffset,\n        globalOffset,\n        display;\n    element = (0,_Observer_js__WEBPACK_IMPORTED_MODULE_0__._getTarget)(trigger, self) || _body;\n    bounds = _getBounds(element) || {};\n\n    if ((!bounds || !bounds.left && !bounds.top) && _getComputedStyle(element).display === \"none\") {\n      // if display is \"none\", it won't report getBoundingClientRect() properly\n      display = element.style.display;\n      element.style.display = \"block\";\n      bounds = _getBounds(element);\n      display ? element.style.display = display : element.style.removeProperty(\"display\");\n    }\n\n    localOffset = _offsetToPx(offsets[0], bounds[direction.d]);\n    globalOffset = _offsetToPx(offsets[1] || \"0\", scrollerSize);\n    value = bounds[direction.p] - scrollerBounds[direction.p] - borderWidth + localOffset + scroll - globalOffset;\n    markerScroller && _positionMarker(markerScroller, globalOffset, direction, scrollerSize - globalOffset < 20 || markerScroller._isStart && globalOffset > 20);\n    scrollerSize -= scrollerSize - globalOffset; // adjust for the marker\n  } else {\n    containerAnimation && (value = gsap.utils.mapRange(containerAnimation.scrollTrigger.start, containerAnimation.scrollTrigger.end, 0, scrollerMax, value));\n    markerScroller && _positionMarker(markerScroller, scrollerSize, direction, true);\n  }\n\n  if (clampZeroProp) {\n    self[clampZeroProp] = value || -0.001;\n    value < 0 && (value = 0);\n  }\n\n  if (marker) {\n    var position = value + scrollerSize,\n        isStart = marker._isStart;\n    p1 = \"scroll\" + direction.d2;\n\n    _positionMarker(marker, position, direction, isStart && position > 20 || !isStart && (useFixedPosition ? Math.max(_body[p1], _docEl[p1]) : marker.parentNode[p1]) <= position + 1);\n\n    if (useFixedPosition) {\n      scrollerBounds = _getBounds(markerScroller);\n      useFixedPosition && (marker.style[direction.op.p] = scrollerBounds[direction.op.p] - direction.op.m - marker._offset + _px);\n    }\n  }\n\n  if (containerAnimation && element) {\n    p1 = _getBounds(element);\n    containerAnimation.seek(scrollerMax);\n    p2 = _getBounds(element);\n    containerAnimation._caScrollDist = p1[direction.p] - p2[direction.p];\n    value = value / containerAnimation._caScrollDist * scrollerMax;\n  }\n\n  containerAnimation && containerAnimation.seek(time);\n  return containerAnimation ? value : Math.round(value);\n},\n    _prefixExp = /(webkit|moz|length|cssText|inset)/i,\n    _reparent = function _reparent(element, parent, top, left) {\n  if (element.parentNode !== parent) {\n    var style = element.style,\n        p,\n        cs;\n\n    if (parent === _body) {\n      element._stOrig = style.cssText; // record original inline styles so we can revert them later\n\n      cs = _getComputedStyle(element);\n\n      for (p in cs) {\n        // must copy all relevant styles to ensure that nothing changes visually when we reparent to the <body>. Skip the vendor prefixed ones.\n        if (!+p && !_prefixExp.test(p) && cs[p] && typeof style[p] === \"string\" && p !== \"0\") {\n          style[p] = cs[p];\n        }\n      }\n\n      style.top = top;\n      style.left = left;\n    } else {\n      style.cssText = element._stOrig;\n    }\n\n    gsap.core.getCache(element).uncache = 1;\n    parent.appendChild(element);\n  }\n},\n    _interruptionTracker = function _interruptionTracker(getValueFunc, initialValue, onInterrupt) {\n  var last1 = initialValue,\n      last2 = last1;\n  return function (value) {\n    var current = Math.round(getValueFunc()); // round because in some [very uncommon] Windows environments, scroll can get reported with decimals even though it was set without.\n\n    if (current !== last1 && current !== last2 && Math.abs(current - last1) > 3 && Math.abs(current - last2) > 3) {\n      // if the user scrolls, kill the tween. iOS Safari intermittently misreports the scroll position, it may be the most recently-set one or the one before that! When Safari is zoomed (CMD-+), it often misreports as 1 pixel off too! So if we set the scroll position to 125, for example, it'll actually report it as 124.\n      value = current;\n      onInterrupt && onInterrupt();\n    }\n\n    last2 = last1;\n    last1 = Math.round(value);\n    return last1;\n  };\n},\n    _shiftMarker = function _shiftMarker(marker, direction, value) {\n  var vars = {};\n  vars[direction.p] = \"+=\" + value;\n  gsap.set(marker, vars);\n},\n    // _mergeAnimations = animations => {\n// \tlet tl = gsap.timeline({smoothChildTiming: true}).startTime(Math.min(...animations.map(a => a.globalTime(0))));\n// \tanimations.forEach(a => {let time = a.totalTime(); tl.add(a); a.totalTime(time); });\n// \ttl.smoothChildTiming = false;\n// \treturn tl;\n// },\n// returns a function that can be used to tween the scroll position in the direction provided, and when doing so it'll add a .tween property to the FUNCTION itself, and remove it when the tween completes or gets killed. This gives us a way to have multiple ScrollTriggers use a central function for any given scroller and see if there's a scroll tween running (which would affect if/how things get updated)\n_getTweenCreator = function _getTweenCreator(scroller, direction) {\n  var getScroll = (0,_Observer_js__WEBPACK_IMPORTED_MODULE_0__._getScrollFunc)(scroller, direction),\n      prop = \"_scroll\" + direction.p2,\n      // add a tweenable property to the scroller that's a getter/setter function, like _scrollTop or _scrollLeft. This way, if someone does gsap.killTweensOf(scroller) it'll kill the scroll tween.\n  getTween = function getTween(scrollTo, vars, initialValue, change1, change2) {\n    var tween = getTween.tween,\n        onComplete = vars.onComplete,\n        modifiers = {};\n    initialValue = initialValue || getScroll();\n\n    var checkForInterruption = _interruptionTracker(getScroll, initialValue, function () {\n      tween.kill();\n      getTween.tween = 0;\n    });\n\n    change2 = change1 && change2 || 0; // if change1 is 0, we set that to the difference and ignore change2. Otherwise, there would be a compound effect.\n\n    change1 = change1 || scrollTo - initialValue;\n    tween && tween.kill();\n    vars[prop] = scrollTo;\n    vars.inherit = false;\n    vars.modifiers = modifiers;\n\n    modifiers[prop] = function () {\n      return checkForInterruption(initialValue + change1 * tween.ratio + change2 * tween.ratio * tween.ratio);\n    };\n\n    vars.onUpdate = function () {\n      _Observer_js__WEBPACK_IMPORTED_MODULE_0__._scrollers.cache++;\n      getTween.tween && _updateAll(); // if it was interrupted/killed, like in a context.revert(), don't force an updateAll()\n    };\n\n    vars.onComplete = function () {\n      getTween.tween = 0;\n      onComplete && onComplete.call(tween);\n    };\n\n    tween = getTween.tween = gsap.to(scroller, vars);\n    return tween;\n  };\n\n  scroller[prop] = getScroll;\n\n  getScroll.wheelHandler = function () {\n    return getTween.tween && getTween.tween.kill() && (getTween.tween = 0);\n  };\n\n  _addListener(scroller, \"wheel\", getScroll.wheelHandler); // Windows machines handle mousewheel scrolling in chunks (like \"3 lines per scroll\") meaning the typical strategy for cancelling the scroll isn't as sensitive. It's much more likely to match one of the previous 2 scroll event positions. So we kill any snapping as soon as there's a wheel event.\n\n\n  ScrollTrigger.isTouch && _addListener(scroller, \"touchmove\", getScroll.wheelHandler);\n  return getTween;\n};\n\nvar ScrollTrigger = /*#__PURE__*/function () {\n  function ScrollTrigger(vars, animation) {\n    _coreInitted || ScrollTrigger.register(gsap) || console.warn(\"Please gsap.registerPlugin(ScrollTrigger)\");\n\n    _context(this);\n\n    this.init(vars, animation);\n  }\n\n  var _proto = ScrollTrigger.prototype;\n\n  _proto.init = function init(vars, animation) {\n    this.progress = this.start = 0;\n    this.vars && this.kill(true, true); // in case it's being initted again\n\n    if (!_enabled) {\n      this.update = this.refresh = this.kill = _passThrough;\n      return;\n    }\n\n    vars = _setDefaults(_isString(vars) || _isNumber(vars) || vars.nodeType ? {\n      trigger: vars\n    } : vars, _defaults);\n\n    var _vars = vars,\n        onUpdate = _vars.onUpdate,\n        toggleClass = _vars.toggleClass,\n        id = _vars.id,\n        onToggle = _vars.onToggle,\n        onRefresh = _vars.onRefresh,\n        scrub = _vars.scrub,\n        trigger = _vars.trigger,\n        pin = _vars.pin,\n        pinSpacing = _vars.pinSpacing,\n        invalidateOnRefresh = _vars.invalidateOnRefresh,\n        anticipatePin = _vars.anticipatePin,\n        onScrubComplete = _vars.onScrubComplete,\n        onSnapComplete = _vars.onSnapComplete,\n        once = _vars.once,\n        snap = _vars.snap,\n        pinReparent = _vars.pinReparent,\n        pinSpacer = _vars.pinSpacer,\n        containerAnimation = _vars.containerAnimation,\n        fastScrollEnd = _vars.fastScrollEnd,\n        preventOverlaps = _vars.preventOverlaps,\n        direction = vars.horizontal || vars.containerAnimation && vars.horizontal !== false ? _Observer_js__WEBPACK_IMPORTED_MODULE_0__._horizontal : _Observer_js__WEBPACK_IMPORTED_MODULE_0__._vertical,\n        isToggle = !scrub && scrub !== 0,\n        scroller = (0,_Observer_js__WEBPACK_IMPORTED_MODULE_0__._getTarget)(vars.scroller || _win),\n        scrollerCache = gsap.core.getCache(scroller),\n        isViewport = _isViewport(scroller),\n        useFixedPosition = (\"pinType\" in vars ? vars.pinType : (0,_Observer_js__WEBPACK_IMPORTED_MODULE_0__._getProxyProp)(scroller, \"pinType\") || isViewport && \"fixed\") === \"fixed\",\n        callbacks = [vars.onEnter, vars.onLeave, vars.onEnterBack, vars.onLeaveBack],\n        toggleActions = isToggle && vars.toggleActions.split(\" \"),\n        markers = \"markers\" in vars ? vars.markers : _defaults.markers,\n        borderWidth = isViewport ? 0 : parseFloat(_getComputedStyle(scroller)[\"border\" + direction.p2 + _Width]) || 0,\n        self = this,\n        onRefreshInit = vars.onRefreshInit && function () {\n      return vars.onRefreshInit(self);\n    },\n        getScrollerSize = _getSizeFunc(scroller, isViewport, direction),\n        getScrollerOffsets = _getOffsetsFunc(scroller, isViewport),\n        lastSnap = 0,\n        lastRefresh = 0,\n        prevProgress = 0,\n        scrollFunc = (0,_Observer_js__WEBPACK_IMPORTED_MODULE_0__._getScrollFunc)(scroller, direction),\n        tweenTo,\n        pinCache,\n        snapFunc,\n        scroll1,\n        scroll2,\n        start,\n        end,\n        markerStart,\n        markerEnd,\n        markerStartTrigger,\n        markerEndTrigger,\n        markerVars,\n        executingOnRefresh,\n        change,\n        pinOriginalState,\n        pinActiveState,\n        pinState,\n        spacer,\n        offset,\n        pinGetter,\n        pinSetter,\n        pinStart,\n        pinChange,\n        spacingStart,\n        spacerState,\n        markerStartSetter,\n        pinMoves,\n        markerEndSetter,\n        cs,\n        snap1,\n        snap2,\n        scrubTween,\n        scrubSmooth,\n        snapDurClamp,\n        snapDelayedCall,\n        prevScroll,\n        prevAnimProgress,\n        caMarkerSetter,\n        customRevertReturn; // for the sake of efficiency, _startClamp/_endClamp serve like a truthy value indicating that clamping was enabled on the start/end, and ALSO store the actual pre-clamped numeric value. We tap into that in ScrollSmoother for speed effects. So for example, if start=\"clamp(top bottom)\" results in a start of -100 naturally, it would get clamped to 0 but -100 would be stored in _startClamp.\n\n\n    self._startClamp = self._endClamp = false;\n    self._dir = direction;\n    anticipatePin *= 45;\n    self.scroller = scroller;\n    self.scroll = containerAnimation ? containerAnimation.time.bind(containerAnimation) : scrollFunc;\n    scroll1 = scrollFunc();\n    self.vars = vars;\n    animation = animation || vars.animation;\n\n    if (\"refreshPriority\" in vars) {\n      _sort = 1;\n      vars.refreshPriority === -9999 && (_primary = self); // used by ScrollSmoother\n    }\n\n    scrollerCache.tweenScroll = scrollerCache.tweenScroll || {\n      top: _getTweenCreator(scroller, _Observer_js__WEBPACK_IMPORTED_MODULE_0__._vertical),\n      left: _getTweenCreator(scroller, _Observer_js__WEBPACK_IMPORTED_MODULE_0__._horizontal)\n    };\n    self.tweenTo = tweenTo = scrollerCache.tweenScroll[direction.p];\n\n    self.scrubDuration = function (value) {\n      scrubSmooth = _isNumber(value) && value;\n\n      if (!scrubSmooth) {\n        scrubTween && scrubTween.progress(1).kill();\n        scrubTween = 0;\n      } else {\n        scrubTween ? scrubTween.duration(value) : scrubTween = gsap.to(animation, {\n          ease: \"expo\",\n          totalProgress: \"+=0\",\n          inherit: false,\n          duration: scrubSmooth,\n          paused: true,\n          onComplete: function onComplete() {\n            return onScrubComplete && onScrubComplete(self);\n          }\n        });\n      }\n    };\n\n    if (animation) {\n      animation.vars.lazy = false;\n      animation._initted && !self.isReverted || animation.vars.immediateRender !== false && vars.immediateRender !== false && animation.duration() && animation.render(0, true, true); // special case: if this ScrollTrigger gets re-initted, a from() tween with a stagger could get initted initially and then reverted on the re-init which means it'll need to get rendered again here to properly display things. Otherwise, See https://gsap.com/forums/topic/36777-scrollsmoother-splittext-nextjs/ and https://codepen.io/GreenSock/pen/eYPyPpd?editors=0010\n\n      self.animation = animation.pause();\n      animation.scrollTrigger = self;\n      self.scrubDuration(scrub);\n      snap1 = 0;\n      id || (id = animation.vars.id);\n    }\n\n    if (snap) {\n      // TODO: potential idea: use legitimate CSS scroll snapping by pushing invisible elements into the DOM that serve as snap positions, and toggle the document.scrollingElement.style.scrollSnapType onToggle. See https://codepen.io/GreenSock/pen/JjLrgWM for a quick proof of concept.\n      if (!_isObject(snap) || snap.push) {\n        snap = {\n          snapTo: snap\n        };\n      }\n\n      \"scrollBehavior\" in _body.style && gsap.set(isViewport ? [_body, _docEl] : scroller, {\n        scrollBehavior: \"auto\"\n      }); // smooth scrolling doesn't work with snap.\n\n      _Observer_js__WEBPACK_IMPORTED_MODULE_0__._scrollers.forEach(function (o) {\n        return _isFunction(o) && o.target === (isViewport ? _doc.scrollingElement || _docEl : scroller) && (o.smooth = false);\n      }); // note: set smooth to false on both the vertical and horizontal scroll getters/setters\n\n\n      snapFunc = _isFunction(snap.snapTo) ? snap.snapTo : snap.snapTo === \"labels\" ? _getClosestLabel(animation) : snap.snapTo === \"labelsDirectional\" ? _getLabelAtDirection(animation) : snap.directional !== false ? function (value, st) {\n        return _snapDirectional(snap.snapTo)(value, _getTime() - lastRefresh < 500 ? 0 : st.direction);\n      } : gsap.utils.snap(snap.snapTo);\n      snapDurClamp = snap.duration || {\n        min: 0.1,\n        max: 2\n      };\n      snapDurClamp = _isObject(snapDurClamp) ? _clamp(snapDurClamp.min, snapDurClamp.max) : _clamp(snapDurClamp, snapDurClamp);\n      snapDelayedCall = gsap.delayedCall(snap.delay || scrubSmooth / 2 || 0.1, function () {\n        var scroll = scrollFunc(),\n            refreshedRecently = _getTime() - lastRefresh < 500,\n            tween = tweenTo.tween;\n\n        if ((refreshedRecently || Math.abs(self.getVelocity()) < 10) && !tween && !_pointerIsDown && lastSnap !== scroll) {\n          var progress = (scroll - start) / change,\n              totalProgress = animation && !isToggle ? animation.totalProgress() : progress,\n              velocity = refreshedRecently ? 0 : (totalProgress - snap2) / (_getTime() - _time2) * 1000 || 0,\n              change1 = gsap.utils.clamp(-progress, 1 - progress, _abs(velocity / 2) * velocity / 0.185),\n              naturalEnd = progress + (snap.inertia === false ? 0 : change1),\n              endValue,\n              endScroll,\n              _snap = snap,\n              onStart = _snap.onStart,\n              _onInterrupt = _snap.onInterrupt,\n              _onComplete = _snap.onComplete;\n          endValue = snapFunc(naturalEnd, self);\n          _isNumber(endValue) || (endValue = naturalEnd); // in case the function didn't return a number, fall back to using the naturalEnd\n\n          endScroll = Math.max(0, Math.round(start + endValue * change));\n\n          if (scroll <= end && scroll >= start && endScroll !== scroll) {\n            if (tween && !tween._initted && tween.data <= _abs(endScroll - scroll)) {\n              // there's an overlapping snap! So we must figure out which one is closer and let that tween live.\n              return;\n            }\n\n            if (snap.inertia === false) {\n              change1 = endValue - progress;\n            }\n\n            tweenTo(endScroll, {\n              duration: snapDurClamp(_abs(Math.max(_abs(naturalEnd - totalProgress), _abs(endValue - totalProgress)) * 0.185 / velocity / 0.05 || 0)),\n              ease: snap.ease || \"power3\",\n              data: _abs(endScroll - scroll),\n              // record the distance so that if another snap tween occurs (conflict) we can prioritize the closest snap.\n              onInterrupt: function onInterrupt() {\n                return snapDelayedCall.restart(true) && _onInterrupt && _onInterrupt(self);\n              },\n              onComplete: function onComplete() {\n                self.update();\n                lastSnap = scrollFunc();\n\n                if (animation && !isToggle) {\n                  // the resolution of the scrollbar is limited, so we should correct the scrubbed animation's playhead at the end to match EXACTLY where it was supposed to snap\n                  scrubTween ? scrubTween.resetTo(\"totalProgress\", endValue, animation._tTime / animation._tDur) : animation.progress(endValue);\n                }\n\n                snap1 = snap2 = animation && !isToggle ? animation.totalProgress() : self.progress;\n                onSnapComplete && onSnapComplete(self);\n                _onComplete && _onComplete(self);\n              }\n            }, scroll, change1 * change, endScroll - scroll - change1 * change);\n            onStart && onStart(self, tweenTo.tween);\n          }\n        } else if (self.isActive && lastSnap !== scroll) {\n          snapDelayedCall.restart(true);\n        }\n      }).pause();\n    }\n\n    id && (_ids[id] = self);\n    trigger = self.trigger = (0,_Observer_js__WEBPACK_IMPORTED_MODULE_0__._getTarget)(trigger || pin !== true && pin); // if a trigger has some kind of scroll-related effect applied that could contaminate the \"y\" or \"x\" position (like a ScrollSmoother effect), we needed a way to temporarily revert it, so we use the stRevert property of the gsCache. It can return another function that we'll call at the end so it can return to its normal state.\n\n    customRevertReturn = trigger && trigger._gsap && trigger._gsap.stRevert;\n    customRevertReturn && (customRevertReturn = customRevertReturn(self));\n    pin = pin === true ? trigger : (0,_Observer_js__WEBPACK_IMPORTED_MODULE_0__._getTarget)(pin);\n    _isString(toggleClass) && (toggleClass = {\n      targets: trigger,\n      className: toggleClass\n    });\n\n    if (pin) {\n      pinSpacing === false || pinSpacing === _margin || (pinSpacing = !pinSpacing && pin.parentNode && pin.parentNode.style && _getComputedStyle(pin.parentNode).display === \"flex\" ? false : _padding); // if the parent is display: flex, don't apply pinSpacing by default. We should check that pin.parentNode is an element (not shadow dom window)\n\n      self.pin = pin;\n      pinCache = gsap.core.getCache(pin);\n\n      if (!pinCache.spacer) {\n        // record the spacer and pinOriginalState on the cache in case someone tries pinning the same element with MULTIPLE ScrollTriggers - we don't want to have multiple spacers or record the \"original\" pin state after it has already been affected by another ScrollTrigger.\n        if (pinSpacer) {\n          pinSpacer = (0,_Observer_js__WEBPACK_IMPORTED_MODULE_0__._getTarget)(pinSpacer);\n          pinSpacer && !pinSpacer.nodeType && (pinSpacer = pinSpacer.current || pinSpacer.nativeElement); // for React & Angular\n\n          pinCache.spacerIsNative = !!pinSpacer;\n          pinSpacer && (pinCache.spacerState = _getState(pinSpacer));\n        }\n\n        pinCache.spacer = spacer = pinSpacer || _doc.createElement(\"div\");\n        spacer.classList.add(\"pin-spacer\");\n        id && spacer.classList.add(\"pin-spacer-\" + id);\n        pinCache.pinState = pinOriginalState = _getState(pin);\n      } else {\n        pinOriginalState = pinCache.pinState;\n      }\n\n      vars.force3D !== false && gsap.set(pin, {\n        force3D: true\n      });\n      self.spacer = spacer = pinCache.spacer;\n      cs = _getComputedStyle(pin);\n      spacingStart = cs[pinSpacing + direction.os2];\n      pinGetter = gsap.getProperty(pin);\n      pinSetter = gsap.quickSetter(pin, direction.a, _px); // pin.firstChild && !_maxScroll(pin, direction) && (pin.style.overflow = \"hidden\"); // protects from collapsing margins, but can have unintended consequences as demonstrated here: https://codepen.io/GreenSock/pen/1e42c7a73bfa409d2cf1e184e7a4248d so it was removed in favor of just telling people to set up their CSS to avoid the collapsing margins (overflow: hidden | auto is just one option. Another is border-top: 1px solid transparent).\n\n      _swapPinIn(pin, spacer, cs);\n\n      pinState = _getState(pin);\n    }\n\n    if (markers) {\n      markerVars = _isObject(markers) ? _setDefaults(markers, _markerDefaults) : _markerDefaults;\n      markerStartTrigger = _createMarker(\"scroller-start\", id, scroller, direction, markerVars, 0);\n      markerEndTrigger = _createMarker(\"scroller-end\", id, scroller, direction, markerVars, 0, markerStartTrigger);\n      offset = markerStartTrigger[\"offset\" + direction.op.d2];\n\n      var content = (0,_Observer_js__WEBPACK_IMPORTED_MODULE_0__._getTarget)((0,_Observer_js__WEBPACK_IMPORTED_MODULE_0__._getProxyProp)(scroller, \"content\") || scroller);\n\n      markerStart = this.markerStart = _createMarker(\"start\", id, content, direction, markerVars, offset, 0, containerAnimation);\n      markerEnd = this.markerEnd = _createMarker(\"end\", id, content, direction, markerVars, offset, 0, containerAnimation);\n      containerAnimation && (caMarkerSetter = gsap.quickSetter([markerStart, markerEnd], direction.a, _px));\n\n      if (!useFixedPosition && !(_Observer_js__WEBPACK_IMPORTED_MODULE_0__._proxies.length && (0,_Observer_js__WEBPACK_IMPORTED_MODULE_0__._getProxyProp)(scroller, \"fixedMarkers\") === true)) {\n        _makePositionable(isViewport ? _body : scroller);\n\n        gsap.set([markerStartTrigger, markerEndTrigger], {\n          force3D: true\n        });\n        markerStartSetter = gsap.quickSetter(markerStartTrigger, direction.a, _px);\n        markerEndSetter = gsap.quickSetter(markerEndTrigger, direction.a, _px);\n      }\n    }\n\n    if (containerAnimation) {\n      var oldOnUpdate = containerAnimation.vars.onUpdate,\n          oldParams = containerAnimation.vars.onUpdateParams;\n      containerAnimation.eventCallback(\"onUpdate\", function () {\n        self.update(0, 0, 1);\n        oldOnUpdate && oldOnUpdate.apply(containerAnimation, oldParams || []);\n      });\n    }\n\n    self.previous = function () {\n      return _triggers[_triggers.indexOf(self) - 1];\n    };\n\n    self.next = function () {\n      return _triggers[_triggers.indexOf(self) + 1];\n    };\n\n    self.revert = function (revert, temp) {\n      if (!temp) {\n        return self.kill(true);\n      } // for compatibility with gsap.context() and gsap.matchMedia() which call revert()\n\n\n      var r = revert !== false || !self.enabled,\n          prevRefreshing = _refreshing;\n\n      if (r !== self.isReverted) {\n        if (r) {\n          prevScroll = Math.max(scrollFunc(), self.scroll.rec || 0); // record the scroll so we can revert later (repositioning/pinning things can affect scroll position). In the static refresh() method, we first record all the scroll positions as a reference.\n\n          prevProgress = self.progress;\n          prevAnimProgress = animation && animation.progress();\n        }\n\n        markerStart && [markerStart, markerEnd, markerStartTrigger, markerEndTrigger].forEach(function (m) {\n          return m.style.display = r ? \"none\" : \"block\";\n        });\n\n        if (r) {\n          _refreshing = self;\n          self.update(r); // make sure the pin is back in its original position so that all the measurements are correct. do this BEFORE swapping the pin out\n        }\n\n        if (pin && (!pinReparent || !self.isActive)) {\n          if (r) {\n            _swapPinOut(pin, spacer, pinOriginalState);\n          } else {\n            _swapPinIn(pin, spacer, _getComputedStyle(pin), spacerState);\n          }\n        }\n\n        r || self.update(r); // when we're restoring, the update should run AFTER swapping the pin into its pin-spacer.\n\n        _refreshing = prevRefreshing; // restore. We set it to true during the update() so that things fire properly in there.\n\n        self.isReverted = r;\n      }\n    };\n\n    self.refresh = function (soft, force, position, pinOffset) {\n      // position is typically only defined if it's coming from setPositions() - it's a way to skip the normal parsing. pinOffset is also only from setPositions() and is mostly related to fancy stuff we need to do in ScrollSmoother with effects\n      if ((_refreshing || !self.enabled) && !force) {\n        return;\n      }\n\n      if (pin && soft && _lastScrollTime) {\n        _addListener(ScrollTrigger, \"scrollEnd\", _softRefresh);\n\n        return;\n      }\n\n      !_refreshingAll && onRefreshInit && onRefreshInit(self);\n      _refreshing = self;\n\n      if (tweenTo.tween && !position) {\n        // we skip this if a position is passed in because typically that's from .setPositions() and it's best to allow in-progress snapping to continue.\n        tweenTo.tween.kill();\n        tweenTo.tween = 0;\n      }\n\n      scrubTween && scrubTween.pause();\n\n      if (invalidateOnRefresh && animation) {\n        animation.revert({\n          kill: false\n        }).invalidate();\n        animation.getChildren && animation.getChildren(true, true, false).forEach(function (t) {\n          return t.vars.immediateRender && t.render(0, true, true);\n        }); // any from() or fromTo() tweens inside a timeline should render immediately (well, unless they have immediateRender: false)\n      }\n\n      self.isReverted || self.revert(true, true);\n      self._subPinOffset = false; // we'll set this to true in the sub-pins if we find any\n\n      var size = getScrollerSize(),\n          scrollerBounds = getScrollerOffsets(),\n          max = containerAnimation ? containerAnimation.duration() : _maxScroll(scroller, direction),\n          isFirstRefresh = change <= 0.01 || !change,\n          offset = 0,\n          otherPinOffset = pinOffset || 0,\n          parsedEnd = _isObject(position) ? position.end : vars.end,\n          parsedEndTrigger = vars.endTrigger || trigger,\n          parsedStart = _isObject(position) ? position.start : vars.start || (vars.start === 0 || !trigger ? 0 : pin ? \"0 0\" : \"0 100%\"),\n          pinnedContainer = self.pinnedContainer = vars.pinnedContainer && (0,_Observer_js__WEBPACK_IMPORTED_MODULE_0__._getTarget)(vars.pinnedContainer, self),\n          triggerIndex = trigger && Math.max(0, _triggers.indexOf(self)) || 0,\n          i = triggerIndex,\n          cs,\n          bounds,\n          scroll,\n          isVertical,\n          override,\n          curTrigger,\n          curPin,\n          oppositeScroll,\n          initted,\n          revertedPins,\n          forcedOverflow,\n          markerStartOffset,\n          markerEndOffset;\n\n      if (markers && _isObject(position)) {\n        // if we alter the start/end positions with .setPositions(), it generally feeds in absolute NUMBERS which don't convey information about where to line up the markers, so to keep it intuitive, we record how far the trigger positions shift after applying the new numbers and then offset by that much in the opposite direction. We do the same to the associated trigger markers too of course.\n        markerStartOffset = gsap.getProperty(markerStartTrigger, direction.p);\n        markerEndOffset = gsap.getProperty(markerEndTrigger, direction.p);\n      }\n\n      while (i-- > 0) {\n        // user might try to pin the same element more than once, so we must find any prior triggers with the same pin, revert them, and determine how long they're pinning so that we can offset things appropriately. Make sure we revert from last to first so that things \"rewind\" properly.\n        curTrigger = _triggers[i];\n        curTrigger.end || curTrigger.refresh(0, 1) || (_refreshing = self); // if it's a timeline-based trigger that hasn't been fully initialized yet because it's waiting for 1 tick, just force the refresh() here, otherwise if it contains a pin that's supposed to affect other ScrollTriggers further down the page, they won't be adjusted properly.\n\n        curPin = curTrigger.pin;\n\n        if (curPin && (curPin === trigger || curPin === pin || curPin === pinnedContainer) && !curTrigger.isReverted) {\n          revertedPins || (revertedPins = []);\n          revertedPins.unshift(curTrigger); // we'll revert from first to last to make sure things reach their end state properly\n\n          curTrigger.revert(true, true);\n        }\n\n        if (curTrigger !== _triggers[i]) {\n          // in case it got removed.\n          triggerIndex--;\n          i--;\n        }\n      }\n\n      _isFunction(parsedStart) && (parsedStart = parsedStart(self));\n      parsedStart = _parseClamp(parsedStart, \"start\", self);\n      start = _parsePosition(parsedStart, trigger, size, direction, scrollFunc(), markerStart, markerStartTrigger, self, scrollerBounds, borderWidth, useFixedPosition, max, containerAnimation, self._startClamp && \"_startClamp\") || (pin ? -0.001 : 0);\n      _isFunction(parsedEnd) && (parsedEnd = parsedEnd(self));\n\n      if (_isString(parsedEnd) && !parsedEnd.indexOf(\"+=\")) {\n        if (~parsedEnd.indexOf(\" \")) {\n          parsedEnd = (_isString(parsedStart) ? parsedStart.split(\" \")[0] : \"\") + parsedEnd;\n        } else {\n          offset = _offsetToPx(parsedEnd.substr(2), size);\n          parsedEnd = _isString(parsedStart) ? parsedStart : (containerAnimation ? gsap.utils.mapRange(0, containerAnimation.duration(), containerAnimation.scrollTrigger.start, containerAnimation.scrollTrigger.end, start) : start) + offset; // _parsePosition won't factor in the offset if the start is a number, so do it here.\n\n          parsedEndTrigger = trigger;\n        }\n      }\n\n      parsedEnd = _parseClamp(parsedEnd, \"end\", self);\n      end = Math.max(start, _parsePosition(parsedEnd || (parsedEndTrigger ? \"100% 0\" : max), parsedEndTrigger, size, direction, scrollFunc() + offset, markerEnd, markerEndTrigger, self, scrollerBounds, borderWidth, useFixedPosition, max, containerAnimation, self._endClamp && \"_endClamp\")) || -0.001;\n      offset = 0;\n      i = triggerIndex;\n\n      while (i--) {\n        curTrigger = _triggers[i];\n        curPin = curTrigger.pin;\n\n        if (curPin && curTrigger.start - curTrigger._pinPush <= start && !containerAnimation && curTrigger.end > 0) {\n          cs = curTrigger.end - (self._startClamp ? Math.max(0, curTrigger.start) : curTrigger.start);\n\n          if ((curPin === trigger && curTrigger.start - curTrigger._pinPush < start || curPin === pinnedContainer) && isNaN(parsedStart)) {\n            // numeric start values shouldn't be offset at all - treat them as absolute\n            offset += cs * (1 - curTrigger.progress);\n          }\n\n          curPin === pin && (otherPinOffset += cs);\n        }\n      }\n\n      start += offset;\n      end += offset;\n      self._startClamp && (self._startClamp += offset);\n\n      if (self._endClamp && !_refreshingAll) {\n        self._endClamp = end || -0.001;\n        end = Math.min(end, _maxScroll(scroller, direction));\n      }\n\n      change = end - start || (start -= 0.01) && 0.001;\n\n      if (isFirstRefresh) {\n        // on the very first refresh(), the prevProgress couldn't have been accurate yet because the start/end were never calculated, so we set it here. Before 3.11.5, it could lead to an inaccurate scroll position restoration with snapping.\n        prevProgress = gsap.utils.clamp(0, 1, gsap.utils.normalize(start, end, prevScroll));\n      }\n\n      self._pinPush = otherPinOffset;\n\n      if (markerStart && offset) {\n        // offset the markers if necessary\n        cs = {};\n        cs[direction.a] = \"+=\" + offset;\n        pinnedContainer && (cs[direction.p] = \"-=\" + scrollFunc());\n        gsap.set([markerStart, markerEnd], cs);\n      }\n\n      if (pin && !(_clampingMax && self.end >= _maxScroll(scroller, direction))) {\n        cs = _getComputedStyle(pin);\n        isVertical = direction === _Observer_js__WEBPACK_IMPORTED_MODULE_0__._vertical;\n        scroll = scrollFunc(); // recalculate because the triggers can affect the scroll\n\n        pinStart = parseFloat(pinGetter(direction.a)) + otherPinOffset;\n\n        if (!max && end > 1) {\n          // makes sure the scroller has a scrollbar, otherwise if something has width: 100%, for example, it would be too big (exclude the scrollbar). See https://gsap.com/forums/topic/25182-scrolltrigger-width-of-page-increase-where-markers-are-set-to-false/\n          forcedOverflow = (isViewport ? _doc.scrollingElement || _docEl : scroller).style;\n          forcedOverflow = {\n            style: forcedOverflow,\n            value: forcedOverflow[\"overflow\" + direction.a.toUpperCase()]\n          };\n\n          if (isViewport && _getComputedStyle(_body)[\"overflow\" + direction.a.toUpperCase()] !== \"scroll\") {\n            // avoid an extra scrollbar if BOTH <html> and <body> have overflow set to \"scroll\"\n            forcedOverflow.style[\"overflow\" + direction.a.toUpperCase()] = \"scroll\";\n          }\n        }\n\n        _swapPinIn(pin, spacer, cs);\n\n        pinState = _getState(pin); // transforms will interfere with the top/left/right/bottom placement, so remove them temporarily. getBoundingClientRect() factors in transforms.\n\n        bounds = _getBounds(pin, true);\n        oppositeScroll = useFixedPosition && (0,_Observer_js__WEBPACK_IMPORTED_MODULE_0__._getScrollFunc)(scroller, isVertical ? _Observer_js__WEBPACK_IMPORTED_MODULE_0__._horizontal : _Observer_js__WEBPACK_IMPORTED_MODULE_0__._vertical)();\n\n        if (pinSpacing) {\n          spacerState = [pinSpacing + direction.os2, change + otherPinOffset + _px];\n          spacerState.t = spacer;\n          i = pinSpacing === _padding ? _getSize(pin, direction) + change + otherPinOffset : 0;\n\n          if (i) {\n            spacerState.push(direction.d, i + _px); // for box-sizing: border-box (must include padding).\n\n            spacer.style.flexBasis !== \"auto\" && (spacer.style.flexBasis = i + _px);\n          }\n\n          _setState(spacerState);\n\n          if (pinnedContainer) {\n            // in ScrollTrigger.refresh(), we need to re-evaluate the pinContainer's size because this pinSpacing may stretch it out, but we can't just add the exact distance because depending on layout, it may not push things down or it may only do so partially.\n            _triggers.forEach(function (t) {\n              if (t.pin === pinnedContainer && t.vars.pinSpacing !== false) {\n                t._subPinOffset = true;\n              }\n            });\n          }\n\n          useFixedPosition && scrollFunc(prevScroll);\n        } else {\n          i = _getSize(pin, direction);\n          i && spacer.style.flexBasis !== \"auto\" && (spacer.style.flexBasis = i + _px);\n        }\n\n        if (useFixedPosition) {\n          override = {\n            top: bounds.top + (isVertical ? scroll - start : oppositeScroll) + _px,\n            left: bounds.left + (isVertical ? oppositeScroll : scroll - start) + _px,\n            boxSizing: \"border-box\",\n            position: \"fixed\"\n          };\n          override[_width] = override[\"max\" + _Width] = Math.ceil(bounds.width) + _px;\n          override[_height] = override[\"max\" + _Height] = Math.ceil(bounds.height) + _px;\n          override[_margin] = override[_margin + _Top] = override[_margin + _Right] = override[_margin + _Bottom] = override[_margin + _Left] = \"0\";\n          override[_padding] = cs[_padding];\n          override[_padding + _Top] = cs[_padding + _Top];\n          override[_padding + _Right] = cs[_padding + _Right];\n          override[_padding + _Bottom] = cs[_padding + _Bottom];\n          override[_padding + _Left] = cs[_padding + _Left];\n          pinActiveState = _copyState(pinOriginalState, override, pinReparent);\n          _refreshingAll && scrollFunc(0);\n        }\n\n        if (animation) {\n          // the animation might be affecting the transform, so we must jump to the end, check the value, and compensate accordingly. Otherwise, when it becomes unpinned, the pinSetter() will get set to a value that doesn't include whatever the animation did.\n          initted = animation._initted; // if not, we must invalidate() after this step, otherwise it could lock in starting values prematurely.\n\n          _suppressOverwrites(1);\n\n          animation.render(animation.duration(), true, true);\n          pinChange = pinGetter(direction.a) - pinStart + change + otherPinOffset;\n          pinMoves = Math.abs(change - pinChange) > 1;\n          useFixedPosition && pinMoves && pinActiveState.splice(pinActiveState.length - 2, 2); // transform is the last property/value set in the state Array. Since the animation is controlling that, we should omit it.\n\n          animation.render(0, true, true);\n          initted || animation.invalidate(true);\n          animation.parent || animation.totalTime(animation.totalTime()); // if, for example, a toggleAction called play() and then refresh() happens and when we render(1) above, it would cause the animation to complete and get removed from its parent, so this makes sure it gets put back in.\n\n          _suppressOverwrites(0);\n        } else {\n          pinChange = change;\n        }\n\n        forcedOverflow && (forcedOverflow.value ? forcedOverflow.style[\"overflow\" + direction.a.toUpperCase()] = forcedOverflow.value : forcedOverflow.style.removeProperty(\"overflow-\" + direction.a));\n      } else if (trigger && scrollFunc() && !containerAnimation) {\n        // it may be INSIDE a pinned element, so walk up the tree and look for any elements with _pinOffset to compensate because anything with pinSpacing that's already scrolled would throw off the measurements in getBoundingClientRect()\n        bounds = trigger.parentNode;\n\n        while (bounds && bounds !== _body) {\n          if (bounds._pinOffset) {\n            start -= bounds._pinOffset;\n            end -= bounds._pinOffset;\n          }\n\n          bounds = bounds.parentNode;\n        }\n      }\n\n      revertedPins && revertedPins.forEach(function (t) {\n        return t.revert(false, true);\n      });\n      self.start = start;\n      self.end = end;\n      scroll1 = scroll2 = _refreshingAll ? prevScroll : scrollFunc(); // reset velocity\n\n      if (!containerAnimation && !_refreshingAll) {\n        scroll1 < prevScroll && scrollFunc(prevScroll);\n        self.scroll.rec = 0;\n      }\n\n      self.revert(false, true);\n      lastRefresh = _getTime();\n\n      if (snapDelayedCall) {\n        lastSnap = -1; // just so snapping gets re-enabled, clear out any recorded last value\n        // self.isActive && scrollFunc(start + change * prevProgress); // previously this line was here to ensure that when snapping kicks in, it's from the previous progress but in some cases that's not desirable, like an all-page ScrollTrigger when new content gets added to the page, that'd totally change the progress.\n\n        snapDelayedCall.restart(true);\n      }\n\n      _refreshing = 0;\n      animation && isToggle && (animation._initted || prevAnimProgress) && animation.progress() !== prevAnimProgress && animation.progress(prevAnimProgress || 0, true).render(animation.time(), true, true); // must force a re-render because if saveStyles() was used on the target(s), the styles could have been wiped out during the refresh().\n\n      if (isFirstRefresh || prevProgress !== self.progress || containerAnimation || invalidateOnRefresh || animation && !animation._initted) {\n        // ensures that the direction is set properly (when refreshing, progress is set back to 0 initially, then back again to wherever it needs to be) and that callbacks are triggered.\n        animation && !isToggle && (animation._initted || prevProgress || animation.vars.immediateRender !== false) && animation.totalProgress(containerAnimation && start < -0.001 && !prevProgress ? gsap.utils.normalize(start, end, 0) : prevProgress, true); // to avoid issues where animation callbacks like onStart aren't triggered.\n\n        self.progress = isFirstRefresh || (scroll1 - start) / change === prevProgress ? 0 : prevProgress;\n      }\n\n      pin && pinSpacing && (spacer._pinOffset = Math.round(self.progress * pinChange));\n      scrubTween && scrubTween.invalidate();\n\n      if (!isNaN(markerStartOffset)) {\n        // numbers were passed in for the position which are absolute, so instead of just putting the markers at the very bottom of the viewport, we figure out how far they shifted down (it's safe to assume they were originally positioned in closer relation to the trigger element with values like \"top\", \"center\", a percentage or whatever, so we offset that much in the opposite direction to basically revert them to the relative position thy were at previously.\n        markerStartOffset -= gsap.getProperty(markerStartTrigger, direction.p);\n        markerEndOffset -= gsap.getProperty(markerEndTrigger, direction.p);\n\n        _shiftMarker(markerStartTrigger, direction, markerStartOffset);\n\n        _shiftMarker(markerStart, direction, markerStartOffset - (pinOffset || 0));\n\n        _shiftMarker(markerEndTrigger, direction, markerEndOffset);\n\n        _shiftMarker(markerEnd, direction, markerEndOffset - (pinOffset || 0));\n      }\n\n      isFirstRefresh && !_refreshingAll && self.update(); // edge case - when you reload a page when it's already scrolled down, some browsers fire a \"scroll\" event before DOMContentLoaded, triggering an updateAll(). If we don't update the self.progress as part of refresh(), then when it happens next, it may record prevProgress as 0 when it really shouldn't, potentially causing a callback in an animation to fire again.\n\n      if (onRefresh && !_refreshingAll && !executingOnRefresh) {\n        // when refreshing all, we do extra work to correct pinnedContainer sizes and ensure things don't exceed the maxScroll, so we should do all the refreshes at the end after all that work so that the start/end values are corrected.\n        executingOnRefresh = true;\n        onRefresh(self);\n        executingOnRefresh = false;\n      }\n    };\n\n    self.getVelocity = function () {\n      return (scrollFunc() - scroll2) / (_getTime() - _time2) * 1000 || 0;\n    };\n\n    self.endAnimation = function () {\n      _endAnimation(self.callbackAnimation);\n\n      if (animation) {\n        scrubTween ? scrubTween.progress(1) : !animation.paused() ? _endAnimation(animation, animation.reversed()) : isToggle || _endAnimation(animation, self.direction < 0, 1);\n      }\n    };\n\n    self.labelToScroll = function (label) {\n      return animation && animation.labels && (start || self.refresh() || start) + animation.labels[label] / animation.duration() * change || 0;\n    };\n\n    self.getTrailing = function (name) {\n      var i = _triggers.indexOf(self),\n          a = self.direction > 0 ? _triggers.slice(0, i).reverse() : _triggers.slice(i + 1);\n\n      return (_isString(name) ? a.filter(function (t) {\n        return t.vars.preventOverlaps === name;\n      }) : a).filter(function (t) {\n        return self.direction > 0 ? t.end <= start : t.start >= end;\n      });\n    };\n\n    self.update = function (reset, recordVelocity, forceFake) {\n      if (containerAnimation && !forceFake && !reset) {\n        return;\n      }\n\n      var scroll = _refreshingAll === true ? prevScroll : self.scroll(),\n          p = reset ? 0 : (scroll - start) / change,\n          clipped = p < 0 ? 0 : p > 1 ? 1 : p || 0,\n          prevProgress = self.progress,\n          isActive,\n          wasActive,\n          toggleState,\n          action,\n          stateChanged,\n          toggled,\n          isAtMax,\n          isTakingAction;\n\n      if (recordVelocity) {\n        scroll2 = scroll1;\n        scroll1 = containerAnimation ? scrollFunc() : scroll;\n\n        if (snap) {\n          snap2 = snap1;\n          snap1 = animation && !isToggle ? animation.totalProgress() : clipped;\n        }\n      } // anticipate the pinning a few ticks ahead of time based on velocity to avoid a visual glitch due to the fact that most browsers do scrolling on a separate thread (not synced with requestAnimationFrame).\n\n\n      if (anticipatePin && pin && !_refreshing && !_startup && _lastScrollTime) {\n        if (!clipped && start < scroll + (scroll - scroll2) / (_getTime() - _time2) * anticipatePin) {\n          clipped = 0.0001;\n        } else if (clipped === 1 && end > scroll + (scroll - scroll2) / (_getTime() - _time2) * anticipatePin) {\n          clipped = 0.9999;\n        }\n      }\n\n      if (clipped !== prevProgress && self.enabled) {\n        isActive = self.isActive = !!clipped && clipped < 1;\n        wasActive = !!prevProgress && prevProgress < 1;\n        toggled = isActive !== wasActive;\n        stateChanged = toggled || !!clipped !== !!prevProgress; // could go from start all the way to end, thus it didn't toggle but it did change state in a sense (may need to fire a callback)\n\n        self.direction = clipped > prevProgress ? 1 : -1;\n        self.progress = clipped;\n\n        if (stateChanged && !_refreshing) {\n          toggleState = clipped && !prevProgress ? 0 : clipped === 1 ? 1 : prevProgress === 1 ? 2 : 3; // 0 = enter, 1 = leave, 2 = enterBack, 3 = leaveBack (we prioritize the FIRST encounter, thus if you scroll really fast past the onEnter and onLeave in one tick, it'd prioritize onEnter.\n\n          if (isToggle) {\n            action = !toggled && toggleActions[toggleState + 1] !== \"none\" && toggleActions[toggleState + 1] || toggleActions[toggleState]; // if it didn't toggle, that means it shot right past and since we prioritize the \"enter\" action, we should switch to the \"leave\" in this case (but only if one is defined)\n\n            isTakingAction = animation && (action === \"complete\" || action === \"reset\" || action in animation);\n          }\n        }\n\n        preventOverlaps && (toggled || isTakingAction) && (isTakingAction || scrub || !animation) && (_isFunction(preventOverlaps) ? preventOverlaps(self) : self.getTrailing(preventOverlaps).forEach(function (t) {\n          return t.endAnimation();\n        }));\n\n        if (!isToggle) {\n          if (scrubTween && !_refreshing && !_startup) {\n            scrubTween._dp._time - scrubTween._start !== scrubTween._time && scrubTween.render(scrubTween._dp._time - scrubTween._start); // if there's a scrub on both the container animation and this one (or a ScrollSmoother), the update order would cause this one not to have rendered yet, so it wouldn't make any progress before we .restart() it heading toward the new progress so it'd appear stuck thus we force a render here.\n\n            if (scrubTween.resetTo) {\n              scrubTween.resetTo(\"totalProgress\", clipped, animation._tTime / animation._tDur);\n            } else {\n              // legacy support (courtesy), before 3.10.0\n              scrubTween.vars.totalProgress = clipped;\n              scrubTween.invalidate().restart();\n            }\n          } else if (animation) {\n            animation.totalProgress(clipped, !!(_refreshing && (lastRefresh || reset)));\n          }\n        }\n\n        if (pin) {\n          reset && pinSpacing && (spacer.style[pinSpacing + direction.os2] = spacingStart);\n\n          if (!useFixedPosition) {\n            pinSetter(_round(pinStart + pinChange * clipped));\n          } else if (stateChanged) {\n            isAtMax = !reset && clipped > prevProgress && end + 1 > scroll && scroll + 1 >= _maxScroll(scroller, direction); // if it's at the VERY end of the page, don't switch away from position: fixed because it's pointless and it could cause a brief flash when the user scrolls back up (when it gets pinned again)\n\n            if (pinReparent) {\n              if (!reset && (isActive || isAtMax)) {\n                var bounds = _getBounds(pin, true),\n                    _offset = scroll - start;\n\n                _reparent(pin, _body, bounds.top + (direction === _Observer_js__WEBPACK_IMPORTED_MODULE_0__._vertical ? _offset : 0) + _px, bounds.left + (direction === _Observer_js__WEBPACK_IMPORTED_MODULE_0__._vertical ? 0 : _offset) + _px);\n              } else {\n                _reparent(pin, spacer);\n              }\n            }\n\n            _setState(isActive || isAtMax ? pinActiveState : pinState);\n\n            pinMoves && clipped < 1 && isActive || pinSetter(pinStart + (clipped === 1 && !isAtMax ? pinChange : 0));\n          }\n        }\n\n        snap && !tweenTo.tween && !_refreshing && !_startup && snapDelayedCall.restart(true);\n        toggleClass && (toggled || once && clipped && (clipped < 1 || !_limitCallbacks)) && _toArray(toggleClass.targets).forEach(function (el) {\n          return el.classList[isActive || once ? \"add\" : \"remove\"](toggleClass.className);\n        }); // classes could affect positioning, so do it even if reset or refreshing is true.\n\n        onUpdate && !isToggle && !reset && onUpdate(self);\n\n        if (stateChanged && !_refreshing) {\n          if (isToggle) {\n            if (isTakingAction) {\n              if (action === \"complete\") {\n                animation.pause().totalProgress(1);\n              } else if (action === \"reset\") {\n                animation.restart(true).pause();\n              } else if (action === \"restart\") {\n                animation.restart(true);\n              } else {\n                animation[action]();\n              }\n            }\n\n            onUpdate && onUpdate(self);\n          }\n\n          if (toggled || !_limitCallbacks) {\n            // on startup, the page could be scrolled and we don't want to fire callbacks that didn't toggle. For example onEnter shouldn't fire if the ScrollTrigger isn't actually entered.\n            onToggle && toggled && _callback(self, onToggle);\n            callbacks[toggleState] && _callback(self, callbacks[toggleState]);\n            once && (clipped === 1 ? self.kill(false, 1) : callbacks[toggleState] = 0); // a callback shouldn't be called again if once is true.\n\n            if (!toggled) {\n              // it's possible to go completely past, like from before the start to after the end (or vice-versa) in which case BOTH callbacks should be fired in that order\n              toggleState = clipped === 1 ? 1 : 3;\n              callbacks[toggleState] && _callback(self, callbacks[toggleState]);\n            }\n          }\n\n          if (fastScrollEnd && !isActive && Math.abs(self.getVelocity()) > (_isNumber(fastScrollEnd) ? fastScrollEnd : 2500)) {\n            _endAnimation(self.callbackAnimation);\n\n            scrubTween ? scrubTween.progress(1) : _endAnimation(animation, action === \"reverse\" ? 1 : !clipped, 1);\n          }\n        } else if (isToggle && onUpdate && !_refreshing) {\n          onUpdate(self);\n        }\n      } // update absolutely-positioned markers (only if the scroller isn't the viewport)\n\n\n      if (markerEndSetter) {\n        var n = containerAnimation ? scroll / containerAnimation.duration() * (containerAnimation._caScrollDist || 0) : scroll;\n        markerStartSetter(n + (markerStartTrigger._isFlipped ? 1 : 0));\n        markerEndSetter(n);\n      }\n\n      caMarkerSetter && caMarkerSetter(-scroll / containerAnimation.duration() * (containerAnimation._caScrollDist || 0));\n    };\n\n    self.enable = function (reset, refresh) {\n      if (!self.enabled) {\n        self.enabled = true;\n\n        _addListener(scroller, \"resize\", _onResize);\n\n        isViewport || _addListener(scroller, \"scroll\", _onScroll);\n        onRefreshInit && _addListener(ScrollTrigger, \"refreshInit\", onRefreshInit);\n\n        if (reset !== false) {\n          self.progress = prevProgress = 0;\n          scroll1 = scroll2 = lastSnap = scrollFunc();\n        }\n\n        refresh !== false && self.refresh();\n      }\n    };\n\n    self.getTween = function (snap) {\n      return snap && tweenTo ? tweenTo.tween : scrubTween;\n    };\n\n    self.setPositions = function (newStart, newEnd, keepClamp, pinOffset) {\n      // doesn't persist after refresh()! Intended to be a way to override values that were set during refresh(), like you could set it in onRefresh()\n      if (containerAnimation) {\n        // convert ratios into scroll positions. Remember, start/end values on ScrollTriggers that have a containerAnimation refer to the time (in seconds), NOT scroll positions.\n        var st = containerAnimation.scrollTrigger,\n            duration = containerAnimation.duration(),\n            _change = st.end - st.start;\n\n        newStart = st.start + _change * newStart / duration;\n        newEnd = st.start + _change * newEnd / duration;\n      }\n\n      self.refresh(false, false, {\n        start: _keepClamp(newStart, keepClamp && !!self._startClamp),\n        end: _keepClamp(newEnd, keepClamp && !!self._endClamp)\n      }, pinOffset);\n      self.update();\n    };\n\n    self.adjustPinSpacing = function (amount) {\n      if (spacerState && amount) {\n        var i = spacerState.indexOf(direction.d) + 1;\n        spacerState[i] = parseFloat(spacerState[i]) + amount + _px;\n        spacerState[1] = parseFloat(spacerState[1]) + amount + _px;\n\n        _setState(spacerState);\n      }\n    };\n\n    self.disable = function (reset, allowAnimation) {\n      if (self.enabled) {\n        reset !== false && self.revert(true, true);\n        self.enabled = self.isActive = false;\n        allowAnimation || scrubTween && scrubTween.pause();\n        prevScroll = 0;\n        pinCache && (pinCache.uncache = 1);\n        onRefreshInit && _removeListener(ScrollTrigger, \"refreshInit\", onRefreshInit);\n\n        if (snapDelayedCall) {\n          snapDelayedCall.pause();\n          tweenTo.tween && tweenTo.tween.kill() && (tweenTo.tween = 0);\n        }\n\n        if (!isViewport) {\n          var i = _triggers.length;\n\n          while (i--) {\n            if (_triggers[i].scroller === scroller && _triggers[i] !== self) {\n              return; //don't remove the listeners if there are still other triggers referencing it.\n            }\n          }\n\n          _removeListener(scroller, \"resize\", _onResize);\n\n          isViewport || _removeListener(scroller, \"scroll\", _onScroll);\n        }\n      }\n    };\n\n    self.kill = function (revert, allowAnimation) {\n      self.disable(revert, allowAnimation);\n      scrubTween && !allowAnimation && scrubTween.kill();\n      id && delete _ids[id];\n\n      var i = _triggers.indexOf(self);\n\n      i >= 0 && _triggers.splice(i, 1);\n      i === _i && _direction > 0 && _i--; // if we're in the middle of a refresh() or update(), splicing would cause skips in the index, so adjust...\n      // if no other ScrollTrigger instances of the same scroller are found, wipe out any recorded scroll position. Otherwise, in a single page application, for example, it could maintain scroll position when it really shouldn't.\n\n      i = 0;\n\n      _triggers.forEach(function (t) {\n        return t.scroller === self.scroller && (i = 1);\n      });\n\n      i || _refreshingAll || (self.scroll.rec = 0);\n\n      if (animation) {\n        animation.scrollTrigger = null;\n        revert && animation.revert({\n          kill: false\n        });\n        allowAnimation || animation.kill();\n      }\n\n      markerStart && [markerStart, markerEnd, markerStartTrigger, markerEndTrigger].forEach(function (m) {\n        return m.parentNode && m.parentNode.removeChild(m);\n      });\n      _primary === self && (_primary = 0);\n\n      if (pin) {\n        pinCache && (pinCache.uncache = 1);\n        i = 0;\n\n        _triggers.forEach(function (t) {\n          return t.pin === pin && i++;\n        });\n\n        i || (pinCache.spacer = 0); // if there aren't any more ScrollTriggers with the same pin, remove the spacer, otherwise it could be contaminated with old/stale values if the user re-creates a ScrollTrigger for the same element.\n      }\n\n      vars.onKill && vars.onKill(self);\n    };\n\n    _triggers.push(self);\n\n    self.enable(false, false);\n    customRevertReturn && customRevertReturn(self);\n\n    if (animation && animation.add && !change) {\n      // if the animation is a timeline, it may not have been populated yet, so it wouldn't render at the proper place on the first refresh(), thus we should schedule one for the next tick. If \"change\" is defined, we know it must be re-enabling, thus we can refresh() right away.\n      var updateFunc = self.update; // some browsers may fire a scroll event BEFORE a tick elapses and/or the DOMContentLoaded fires. So there's a chance update() will be called BEFORE a refresh() has happened on a Timeline-attached ScrollTrigger which means the start/end won't be calculated yet. We don't want to add conditional logic inside the update() method (like check to see if end is defined and if not, force a refresh()) because that's a function that gets hit a LOT (performance). So we swap out the real update() method for this one that'll re-attach it the first time it gets called and of course forces a refresh().\n\n      self.update = function () {\n        self.update = updateFunc;\n        _Observer_js__WEBPACK_IMPORTED_MODULE_0__._scrollers.cache++; // otherwise a cached scroll position may get used in the refresh() in a very rare scenario, like if ScrollTriggers are created inside a DOMContentLoaded event and the queued requestAnimationFrame() fires beforehand. See https://gsap.com/community/forums/topic/41267-scrolltrigger-breaks-on-refresh-when-using-domcontentloaded/\n\n        start || end || self.refresh();\n      };\n\n      gsap.delayedCall(0.01, self.update);\n      change = 0.01;\n      start = end = 0;\n    } else {\n      self.refresh();\n    }\n\n    pin && _queueRefreshAll(); // pinning could affect the positions of other things, so make sure we queue a full refresh()\n  };\n\n  ScrollTrigger.register = function register(core) {\n    if (!_coreInitted) {\n      gsap = core || _getGSAP();\n      _windowExists() && window.document && ScrollTrigger.enable();\n      _coreInitted = _enabled;\n    }\n\n    return _coreInitted;\n  };\n\n  ScrollTrigger.defaults = function defaults(config) {\n    if (config) {\n      for (var p in config) {\n        _defaults[p] = config[p];\n      }\n    }\n\n    return _defaults;\n  };\n\n  ScrollTrigger.disable = function disable(reset, kill) {\n    _enabled = 0;\n\n    _triggers.forEach(function (trigger) {\n      return trigger[kill ? \"kill\" : \"disable\"](reset);\n    });\n\n    _removeListener(_win, \"wheel\", _onScroll);\n\n    _removeListener(_doc, \"scroll\", _onScroll);\n\n    clearInterval(_syncInterval);\n\n    _removeListener(_doc, \"touchcancel\", _passThrough);\n\n    _removeListener(_body, \"touchstart\", _passThrough);\n\n    _multiListener(_removeListener, _doc, \"pointerdown,touchstart,mousedown\", _pointerDownHandler);\n\n    _multiListener(_removeListener, _doc, \"pointerup,touchend,mouseup\", _pointerUpHandler);\n\n    _resizeDelay.kill();\n\n    _iterateAutoRefresh(_removeListener);\n\n    for (var i = 0; i < _Observer_js__WEBPACK_IMPORTED_MODULE_0__._scrollers.length; i += 3) {\n      _wheelListener(_removeListener, _Observer_js__WEBPACK_IMPORTED_MODULE_0__._scrollers[i], _Observer_js__WEBPACK_IMPORTED_MODULE_0__._scrollers[i + 1]);\n\n      _wheelListener(_removeListener, _Observer_js__WEBPACK_IMPORTED_MODULE_0__._scrollers[i], _Observer_js__WEBPACK_IMPORTED_MODULE_0__._scrollers[i + 2]);\n    }\n  };\n\n  ScrollTrigger.enable = function enable() {\n    _win = window;\n    _doc = document;\n    _docEl = _doc.documentElement;\n    _body = _doc.body;\n\n    if (gsap) {\n      _toArray = gsap.utils.toArray;\n      _clamp = gsap.utils.clamp;\n      _context = gsap.core.context || _passThrough;\n      _suppressOverwrites = gsap.core.suppressOverwrites || _passThrough;\n      _scrollRestoration = _win.history.scrollRestoration || \"auto\";\n      _lastScroll = _win.pageYOffset || 0;\n      gsap.core.globals(\"ScrollTrigger\", ScrollTrigger); // must register the global manually because in Internet Explorer, functions (classes) don't have a \"name\" property.\n\n      if (_body) {\n        _enabled = 1;\n        _div100vh = document.createElement(\"div\"); // to solve mobile browser address bar show/hide resizing, we shouldn't rely on window.innerHeight. Instead, use a <div> with its height set to 100vh and measure that since that's what the scrolling is based on anyway and it's not affected by address bar showing/hiding.\n\n        _div100vh.style.height = \"100vh\";\n        _div100vh.style.position = \"absolute\";\n\n        _refresh100vh();\n\n        _rafBugFix();\n\n        _Observer_js__WEBPACK_IMPORTED_MODULE_0__.Observer.register(gsap); // isTouch is 0 if no touch, 1 if ONLY touch, and 2 if it can accommodate touch but also other types like mouse/pointer.\n\n        ScrollTrigger.isTouch = _Observer_js__WEBPACK_IMPORTED_MODULE_0__.Observer.isTouch;\n        _fixIOSBug = _Observer_js__WEBPACK_IMPORTED_MODULE_0__.Observer.isTouch && /(iPad|iPhone|iPod|Mac)/g.test(navigator.userAgent); // since 2017, iOS has had a bug that causes event.clientX/Y to be inaccurate when a scroll occurs, thus we must alternate ignoring every other touchmove event to work around it. See https://bugs.webkit.org/show_bug.cgi?id=181954 and https://codepen.io/GreenSock/pen/ExbrPNa/087cef197dc35445a0951e8935c41503\n\n        _ignoreMobileResize = _Observer_js__WEBPACK_IMPORTED_MODULE_0__.Observer.isTouch === 1;\n\n        _addListener(_win, \"wheel\", _onScroll); // mostly for 3rd party smooth scrolling libraries.\n\n\n        _root = [_win, _doc, _docEl, _body];\n\n        if (gsap.matchMedia) {\n          ScrollTrigger.matchMedia = function (vars) {\n            var mm = gsap.matchMedia(),\n                p;\n\n            for (p in vars) {\n              mm.add(p, vars[p]);\n            }\n\n            return mm;\n          };\n\n          gsap.addEventListener(\"matchMediaInit\", function () {\n            return _revertAll();\n          });\n          gsap.addEventListener(\"matchMediaRevert\", function () {\n            return _revertRecorded();\n          });\n          gsap.addEventListener(\"matchMedia\", function () {\n            _refreshAll(0, 1);\n\n            _dispatch(\"matchMedia\");\n          });\n          gsap.matchMedia().add(\"(orientation: portrait)\", function () {\n            // when orientation changes, we should take new base measurements for the ignoreMobileResize feature.\n            _setBaseDimensions();\n\n            return _setBaseDimensions;\n          });\n        } else {\n          console.warn(\"Requires GSAP 3.11.0 or later\");\n        }\n\n        _setBaseDimensions();\n\n        _addListener(_doc, \"scroll\", _onScroll); // some browsers (like Chrome), the window stops dispatching scroll events on the window if you scroll really fast, but it's consistent on the document!\n\n\n        var bodyHasStyle = _body.hasAttribute(\"style\"),\n            bodyStyle = _body.style,\n            border = bodyStyle.borderTopStyle,\n            AnimationProto = gsap.core.Animation.prototype,\n            bounds,\n            i;\n\n        AnimationProto.revert || Object.defineProperty(AnimationProto, \"revert\", {\n          value: function value() {\n            return this.time(-0.01, true);\n          }\n        }); // only for backwards compatibility (Animation.revert() was added after 3.10.4)\n\n        bodyStyle.borderTopStyle = \"solid\"; // works around an issue where a margin of a child element could throw off the bounds of the _body, making it seem like there's a margin when there actually isn't. The border ensures that the bounds are accurate.\n\n        bounds = _getBounds(_body);\n        _Observer_js__WEBPACK_IMPORTED_MODULE_0__._vertical.m = Math.round(bounds.top + _Observer_js__WEBPACK_IMPORTED_MODULE_0__._vertical.sc()) || 0; // accommodate the offset of the <body> caused by margins and/or padding\n\n        _Observer_js__WEBPACK_IMPORTED_MODULE_0__._horizontal.m = Math.round(bounds.left + _Observer_js__WEBPACK_IMPORTED_MODULE_0__._horizontal.sc()) || 0;\n        border ? bodyStyle.borderTopStyle = border : bodyStyle.removeProperty(\"border-top-style\");\n\n        if (!bodyHasStyle) {\n          // SSR frameworks like Next.js complain if this attribute gets added.\n          _body.setAttribute(\"style\", \"\"); // it's not enough to just removeAttribute() - we must first set it to empty, otherwise Next.js complains.\n\n\n          _body.removeAttribute(\"style\");\n        } // TODO: (?) maybe move to leveraging the velocity mechanism in Observer and skip intervals.\n\n\n        _syncInterval = setInterval(_sync, 250);\n        gsap.delayedCall(0.5, function () {\n          return _startup = 0;\n        });\n\n        _addListener(_doc, \"touchcancel\", _passThrough); // some older Android devices intermittently stop dispatching \"touchmove\" events if we don't listen for \"touchcancel\" on the document.\n\n\n        _addListener(_body, \"touchstart\", _passThrough); //works around Safari bug: https://gsap.com/forums/topic/21450-draggable-in-iframe-on-mobile-is-buggy/\n\n\n        _multiListener(_addListener, _doc, \"pointerdown,touchstart,mousedown\", _pointerDownHandler);\n\n        _multiListener(_addListener, _doc, \"pointerup,touchend,mouseup\", _pointerUpHandler);\n\n        _transformProp = gsap.utils.checkPrefix(\"transform\");\n\n        _stateProps.push(_transformProp);\n\n        _coreInitted = _getTime();\n        _resizeDelay = gsap.delayedCall(0.2, _refreshAll).pause();\n        _autoRefresh = [_doc, \"visibilitychange\", function () {\n          var w = _win.innerWidth,\n              h = _win.innerHeight;\n\n          if (_doc.hidden) {\n            _prevWidth = w;\n            _prevHeight = h;\n          } else if (_prevWidth !== w || _prevHeight !== h) {\n            _onResize();\n          }\n        }, _doc, \"DOMContentLoaded\", _refreshAll, _win, \"load\", _refreshAll, _win, \"resize\", _onResize];\n\n        _iterateAutoRefresh(_addListener);\n\n        _triggers.forEach(function (trigger) {\n          return trigger.enable(0, 1);\n        });\n\n        for (i = 0; i < _Observer_js__WEBPACK_IMPORTED_MODULE_0__._scrollers.length; i += 3) {\n          _wheelListener(_removeListener, _Observer_js__WEBPACK_IMPORTED_MODULE_0__._scrollers[i], _Observer_js__WEBPACK_IMPORTED_MODULE_0__._scrollers[i + 1]);\n\n          _wheelListener(_removeListener, _Observer_js__WEBPACK_IMPORTED_MODULE_0__._scrollers[i], _Observer_js__WEBPACK_IMPORTED_MODULE_0__._scrollers[i + 2]);\n        }\n      }\n    }\n  };\n\n  ScrollTrigger.config = function config(vars) {\n    \"limitCallbacks\" in vars && (_limitCallbacks = !!vars.limitCallbacks);\n    var ms = vars.syncInterval;\n    ms && clearInterval(_syncInterval) || (_syncInterval = ms) && setInterval(_sync, ms);\n    \"ignoreMobileResize\" in vars && (_ignoreMobileResize = ScrollTrigger.isTouch === 1 && vars.ignoreMobileResize);\n\n    if (\"autoRefreshEvents\" in vars) {\n      _iterateAutoRefresh(_removeListener) || _iterateAutoRefresh(_addListener, vars.autoRefreshEvents || \"none\");\n      _ignoreResize = (vars.autoRefreshEvents + \"\").indexOf(\"resize\") === -1;\n    }\n  };\n\n  ScrollTrigger.scrollerProxy = function scrollerProxy(target, vars) {\n    var t = (0,_Observer_js__WEBPACK_IMPORTED_MODULE_0__._getTarget)(target),\n        i = _Observer_js__WEBPACK_IMPORTED_MODULE_0__._scrollers.indexOf(t),\n        isViewport = _isViewport(t);\n\n    if (~i) {\n      _Observer_js__WEBPACK_IMPORTED_MODULE_0__._scrollers.splice(i, isViewport ? 6 : 2);\n    }\n\n    if (vars) {\n      isViewport ? _Observer_js__WEBPACK_IMPORTED_MODULE_0__._proxies.unshift(_win, vars, _body, vars, _docEl, vars) : _Observer_js__WEBPACK_IMPORTED_MODULE_0__._proxies.unshift(t, vars);\n    }\n  };\n\n  ScrollTrigger.clearMatchMedia = function clearMatchMedia(query) {\n    _triggers.forEach(function (t) {\n      return t._ctx && t._ctx.query === query && t._ctx.kill(true, true);\n    });\n  };\n\n  ScrollTrigger.isInViewport = function isInViewport(element, ratio, horizontal) {\n    var bounds = (_isString(element) ? (0,_Observer_js__WEBPACK_IMPORTED_MODULE_0__._getTarget)(element) : element).getBoundingClientRect(),\n        offset = bounds[horizontal ? _width : _height] * ratio || 0;\n    return horizontal ? bounds.right - offset > 0 && bounds.left + offset < _win.innerWidth : bounds.bottom - offset > 0 && bounds.top + offset < _win.innerHeight;\n  };\n\n  ScrollTrigger.positionInViewport = function positionInViewport(element, referencePoint, horizontal) {\n    _isString(element) && (element = (0,_Observer_js__WEBPACK_IMPORTED_MODULE_0__._getTarget)(element));\n    var bounds = element.getBoundingClientRect(),\n        size = bounds[horizontal ? _width : _height],\n        offset = referencePoint == null ? size / 2 : referencePoint in _keywords ? _keywords[referencePoint] * size : ~referencePoint.indexOf(\"%\") ? parseFloat(referencePoint) * size / 100 : parseFloat(referencePoint) || 0;\n    return horizontal ? (bounds.left + offset) / _win.innerWidth : (bounds.top + offset) / _win.innerHeight;\n  };\n\n  ScrollTrigger.killAll = function killAll(allowListeners) {\n    _triggers.slice(0).forEach(function (t) {\n      return t.vars.id !== \"ScrollSmoother\" && t.kill();\n    });\n\n    if (allowListeners !== true) {\n      var listeners = _listeners.killAll || [];\n      _listeners = {};\n      listeners.forEach(function (f) {\n        return f();\n      });\n    }\n  };\n\n  return ScrollTrigger;\n}();\nScrollTrigger.version = \"3.13.0\";\n\nScrollTrigger.saveStyles = function (targets) {\n  return targets ? _toArray(targets).forEach(function (target) {\n    // saved styles are recorded in a consecutive alternating Array, like [element, cssText, transform attribute, cache, matchMedia, ...]\n    if (target && target.style) {\n      var i = _savedStyles.indexOf(target);\n\n      i >= 0 && _savedStyles.splice(i, 5);\n\n      _savedStyles.push(target, target.style.cssText, target.getBBox && target.getAttribute(\"transform\"), gsap.core.getCache(target), _context());\n    }\n  }) : _savedStyles;\n};\n\nScrollTrigger.revert = function (soft, media) {\n  return _revertAll(!soft, media);\n};\n\nScrollTrigger.create = function (vars, animation) {\n  return new ScrollTrigger(vars, animation);\n};\n\nScrollTrigger.refresh = function (safe) {\n  return safe ? _onResize(true) : (_coreInitted || ScrollTrigger.register()) && _refreshAll(true);\n};\n\nScrollTrigger.update = function (force) {\n  return ++_Observer_js__WEBPACK_IMPORTED_MODULE_0__._scrollers.cache && _updateAll(force === true ? 2 : 0);\n};\n\nScrollTrigger.clearScrollMemory = _clearScrollMemory;\n\nScrollTrigger.maxScroll = function (element, horizontal) {\n  return _maxScroll(element, horizontal ? _Observer_js__WEBPACK_IMPORTED_MODULE_0__._horizontal : _Observer_js__WEBPACK_IMPORTED_MODULE_0__._vertical);\n};\n\nScrollTrigger.getScrollFunc = function (element, horizontal) {\n  return (0,_Observer_js__WEBPACK_IMPORTED_MODULE_0__._getScrollFunc)((0,_Observer_js__WEBPACK_IMPORTED_MODULE_0__._getTarget)(element), horizontal ? _Observer_js__WEBPACK_IMPORTED_MODULE_0__._horizontal : _Observer_js__WEBPACK_IMPORTED_MODULE_0__._vertical);\n};\n\nScrollTrigger.getById = function (id) {\n  return _ids[id];\n};\n\nScrollTrigger.getAll = function () {\n  return _triggers.filter(function (t) {\n    return t.vars.id !== \"ScrollSmoother\";\n  });\n}; // it's common for people to ScrollTrigger.getAll(t => t.kill()) on page routes, for example, and we don't want it to ruin smooth scrolling by killing the main ScrollSmoother one.\n\n\nScrollTrigger.isScrolling = function () {\n  return !!_lastScrollTime;\n};\n\nScrollTrigger.snapDirectional = _snapDirectional;\n\nScrollTrigger.addEventListener = function (type, callback) {\n  var a = _listeners[type] || (_listeners[type] = []);\n  ~a.indexOf(callback) || a.push(callback);\n};\n\nScrollTrigger.removeEventListener = function (type, callback) {\n  var a = _listeners[type],\n      i = a && a.indexOf(callback);\n  i >= 0 && a.splice(i, 1);\n};\n\nScrollTrigger.batch = function (targets, vars) {\n  var result = [],\n      varsCopy = {},\n      interval = vars.interval || 0.016,\n      batchMax = vars.batchMax || 1e9,\n      proxyCallback = function proxyCallback(type, callback) {\n    var elements = [],\n        triggers = [],\n        delay = gsap.delayedCall(interval, function () {\n      callback(elements, triggers);\n      elements = [];\n      triggers = [];\n    }).pause();\n    return function (self) {\n      elements.length || delay.restart(true);\n      elements.push(self.trigger);\n      triggers.push(self);\n      batchMax <= elements.length && delay.progress(1);\n    };\n  },\n      p;\n\n  for (p in vars) {\n    varsCopy[p] = p.substr(0, 2) === \"on\" && _isFunction(vars[p]) && p !== \"onRefreshInit\" ? proxyCallback(p, vars[p]) : vars[p];\n  }\n\n  if (_isFunction(batchMax)) {\n    batchMax = batchMax();\n\n    _addListener(ScrollTrigger, \"refresh\", function () {\n      return batchMax = vars.batchMax();\n    });\n  }\n\n  _toArray(targets).forEach(function (target) {\n    var config = {};\n\n    for (p in varsCopy) {\n      config[p] = varsCopy[p];\n    }\n\n    config.trigger = target;\n    result.push(ScrollTrigger.create(config));\n  });\n\n  return result;\n}; // to reduce file size. clamps the scroll and also returns a duration multiplier so that if the scroll gets chopped shorter, the duration gets curtailed as well (otherwise if you're very close to the top of the page, for example, and swipe up really fast, it'll suddenly slow down and take a long time to reach the top).\n\n\nvar _clampScrollAndGetDurationMultiplier = function _clampScrollAndGetDurationMultiplier(scrollFunc, current, end, max) {\n  current > max ? scrollFunc(max) : current < 0 && scrollFunc(0);\n  return end > max ? (max - current) / (end - current) : end < 0 ? current / (current - end) : 1;\n},\n    _allowNativePanning = function _allowNativePanning(target, direction) {\n  if (direction === true) {\n    target.style.removeProperty(\"touch-action\");\n  } else {\n    target.style.touchAction = direction === true ? \"auto\" : direction ? \"pan-\" + direction + (_Observer_js__WEBPACK_IMPORTED_MODULE_0__.Observer.isTouch ? \" pinch-zoom\" : \"\") : \"none\"; // note: Firefox doesn't support it pinch-zoom properly, at least in addition to a pan-x or pan-y.\n  }\n\n  target === _docEl && _allowNativePanning(_body, direction);\n},\n    _overflow = {\n  auto: 1,\n  scroll: 1\n},\n    _nestedScroll = function _nestedScroll(_ref5) {\n  var event = _ref5.event,\n      target = _ref5.target,\n      axis = _ref5.axis;\n\n  var node = (event.changedTouches ? event.changedTouches[0] : event).target,\n      cache = node._gsap || gsap.core.getCache(node),\n      time = _getTime(),\n      cs;\n\n  if (!cache._isScrollT || time - cache._isScrollT > 2000) {\n    // cache for 2 seconds to improve performance.\n    while (node && node !== _body && (node.scrollHeight <= node.clientHeight && node.scrollWidth <= node.clientWidth || !(_overflow[(cs = _getComputedStyle(node)).overflowY] || _overflow[cs.overflowX]))) {\n      node = node.parentNode;\n    }\n\n    cache._isScroll = node && node !== target && !_isViewport(node) && (_overflow[(cs = _getComputedStyle(node)).overflowY] || _overflow[cs.overflowX]);\n    cache._isScrollT = time;\n  }\n\n  if (cache._isScroll || axis === \"x\") {\n    event.stopPropagation();\n    event._gsapAllow = true;\n  }\n},\n    // capture events on scrollable elements INSIDE the <body> and allow those by calling stopPropagation() when we find a scrollable ancestor\n_inputObserver = function _inputObserver(target, type, inputs, nested) {\n  return _Observer_js__WEBPACK_IMPORTED_MODULE_0__.Observer.create({\n    target: target,\n    capture: true,\n    debounce: false,\n    lockAxis: true,\n    type: type,\n    onWheel: nested = nested && _nestedScroll,\n    onPress: nested,\n    onDrag: nested,\n    onScroll: nested,\n    onEnable: function onEnable() {\n      return inputs && _addListener(_doc, _Observer_js__WEBPACK_IMPORTED_MODULE_0__.Observer.eventTypes[0], _captureInputs, false, true);\n    },\n    onDisable: function onDisable() {\n      return _removeListener(_doc, _Observer_js__WEBPACK_IMPORTED_MODULE_0__.Observer.eventTypes[0], _captureInputs, true);\n    }\n  });\n},\n    _inputExp = /(input|label|select|textarea)/i,\n    _inputIsFocused,\n    _captureInputs = function _captureInputs(e) {\n  var isInput = _inputExp.test(e.target.tagName);\n\n  if (isInput || _inputIsFocused) {\n    e._gsapAllow = true;\n    _inputIsFocused = isInput;\n  }\n},\n    _getScrollNormalizer = function _getScrollNormalizer(vars) {\n  _isObject(vars) || (vars = {});\n  vars.preventDefault = vars.isNormalizer = vars.allowClicks = true;\n  vars.type || (vars.type = \"wheel,touch\");\n  vars.debounce = !!vars.debounce;\n  vars.id = vars.id || \"normalizer\";\n\n  var _vars2 = vars,\n      normalizeScrollX = _vars2.normalizeScrollX,\n      momentum = _vars2.momentum,\n      allowNestedScroll = _vars2.allowNestedScroll,\n      onRelease = _vars2.onRelease,\n      self,\n      maxY,\n      target = (0,_Observer_js__WEBPACK_IMPORTED_MODULE_0__._getTarget)(vars.target) || _docEl,\n      smoother = gsap.core.globals().ScrollSmoother,\n      smootherInstance = smoother && smoother.get(),\n      content = _fixIOSBug && (vars.content && (0,_Observer_js__WEBPACK_IMPORTED_MODULE_0__._getTarget)(vars.content) || smootherInstance && vars.content !== false && !smootherInstance.smooth() && smootherInstance.content()),\n      scrollFuncY = (0,_Observer_js__WEBPACK_IMPORTED_MODULE_0__._getScrollFunc)(target, _Observer_js__WEBPACK_IMPORTED_MODULE_0__._vertical),\n      scrollFuncX = (0,_Observer_js__WEBPACK_IMPORTED_MODULE_0__._getScrollFunc)(target, _Observer_js__WEBPACK_IMPORTED_MODULE_0__._horizontal),\n      scale = 1,\n      initialScale = (_Observer_js__WEBPACK_IMPORTED_MODULE_0__.Observer.isTouch && _win.visualViewport ? _win.visualViewport.scale * _win.visualViewport.width : _win.outerWidth) / _win.innerWidth,\n      wheelRefresh = 0,\n      resolveMomentumDuration = _isFunction(momentum) ? function () {\n    return momentum(self);\n  } : function () {\n    return momentum || 2.8;\n  },\n      lastRefreshID,\n      skipTouchMove,\n      inputObserver = _inputObserver(target, vars.type, true, allowNestedScroll),\n      resumeTouchMove = function resumeTouchMove() {\n    return skipTouchMove = false;\n  },\n      scrollClampX = _passThrough,\n      scrollClampY = _passThrough,\n      updateClamps = function updateClamps() {\n    maxY = _maxScroll(target, _Observer_js__WEBPACK_IMPORTED_MODULE_0__._vertical);\n    scrollClampY = _clamp(_fixIOSBug ? 1 : 0, maxY);\n    normalizeScrollX && (scrollClampX = _clamp(0, _maxScroll(target, _Observer_js__WEBPACK_IMPORTED_MODULE_0__._horizontal)));\n    lastRefreshID = _refreshID;\n  },\n      removeContentOffset = function removeContentOffset() {\n    content._gsap.y = _round(parseFloat(content._gsap.y) + scrollFuncY.offset) + \"px\";\n    content.style.transform = \"matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, \" + parseFloat(content._gsap.y) + \", 0, 1)\";\n    scrollFuncY.offset = scrollFuncY.cacheID = 0;\n  },\n      ignoreDrag = function ignoreDrag() {\n    if (skipTouchMove) {\n      requestAnimationFrame(resumeTouchMove);\n\n      var offset = _round(self.deltaY / 2),\n          scroll = scrollClampY(scrollFuncY.v - offset);\n\n      if (content && scroll !== scrollFuncY.v + scrollFuncY.offset) {\n        scrollFuncY.offset = scroll - scrollFuncY.v;\n\n        var y = _round((parseFloat(content && content._gsap.y) || 0) - scrollFuncY.offset);\n\n        content.style.transform = \"matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, \" + y + \", 0, 1)\";\n        content._gsap.y = y + \"px\";\n        scrollFuncY.cacheID = _Observer_js__WEBPACK_IMPORTED_MODULE_0__._scrollers.cache;\n\n        _updateAll();\n      }\n\n      return true;\n    }\n\n    scrollFuncY.offset && removeContentOffset();\n    skipTouchMove = true;\n  },\n      tween,\n      startScrollX,\n      startScrollY,\n      onStopDelayedCall,\n      onResize = function onResize() {\n    // if the window resizes, like on an iPhone which Apple FORCES the address bar to show/hide even if we event.preventDefault(), it may be scrolling too far now that the address bar is showing, so we must dynamically adjust the momentum tween.\n    updateClamps();\n\n    if (tween.isActive() && tween.vars.scrollY > maxY) {\n      scrollFuncY() > maxY ? tween.progress(1) && scrollFuncY(maxY) : tween.resetTo(\"scrollY\", maxY);\n    }\n  };\n\n  content && gsap.set(content, {\n    y: \"+=0\"\n  }); // to ensure there's a cache (element._gsap)\n\n  vars.ignoreCheck = function (e) {\n    return _fixIOSBug && e.type === \"touchmove\" && ignoreDrag(e) || scale > 1.05 && e.type !== \"touchstart\" || self.isGesturing || e.touches && e.touches.length > 1;\n  };\n\n  vars.onPress = function () {\n    skipTouchMove = false;\n    var prevScale = scale;\n    scale = _round((_win.visualViewport && _win.visualViewport.scale || 1) / initialScale);\n    tween.pause();\n    prevScale !== scale && _allowNativePanning(target, scale > 1.01 ? true : normalizeScrollX ? false : \"x\");\n    startScrollX = scrollFuncX();\n    startScrollY = scrollFuncY();\n    updateClamps();\n    lastRefreshID = _refreshID;\n  };\n\n  vars.onRelease = vars.onGestureStart = function (self, wasDragging) {\n    scrollFuncY.offset && removeContentOffset();\n\n    if (!wasDragging) {\n      onStopDelayedCall.restart(true);\n    } else {\n      _Observer_js__WEBPACK_IMPORTED_MODULE_0__._scrollers.cache++; // make sure we're pulling the non-cached value\n      // alternate algorithm: durX = Math.min(6, Math.abs(self.velocityX / 800)),\tdur = Math.max(durX, Math.min(6, Math.abs(self.velocityY / 800))); dur = dur * (0.4 + (1 - _power4In(dur / 6)) * 0.6)) * (momentumSpeed || 1)\n\n      var dur = resolveMomentumDuration(),\n          currentScroll,\n          endScroll;\n\n      if (normalizeScrollX) {\n        currentScroll = scrollFuncX();\n        endScroll = currentScroll + dur * 0.05 * -self.velocityX / 0.227; // the constant .227 is from power4(0.05). velocity is inverted because scrolling goes in the opposite direction.\n\n        dur *= _clampScrollAndGetDurationMultiplier(scrollFuncX, currentScroll, endScroll, _maxScroll(target, _Observer_js__WEBPACK_IMPORTED_MODULE_0__._horizontal));\n        tween.vars.scrollX = scrollClampX(endScroll);\n      }\n\n      currentScroll = scrollFuncY();\n      endScroll = currentScroll + dur * 0.05 * -self.velocityY / 0.227; // the constant .227 is from power4(0.05)\n\n      dur *= _clampScrollAndGetDurationMultiplier(scrollFuncY, currentScroll, endScroll, _maxScroll(target, _Observer_js__WEBPACK_IMPORTED_MODULE_0__._vertical));\n      tween.vars.scrollY = scrollClampY(endScroll);\n      tween.invalidate().duration(dur).play(0.01);\n\n      if (_fixIOSBug && tween.vars.scrollY >= maxY || currentScroll >= maxY - 1) {\n        // iOS bug: it'll show the address bar but NOT fire the window \"resize\" event until the animation is done but we must protect against overshoot so we leverage an onUpdate to do so.\n        gsap.to({}, {\n          onUpdate: onResize,\n          duration: dur\n        });\n      }\n    }\n\n    onRelease && onRelease(self);\n  };\n\n  vars.onWheel = function () {\n    tween._ts && tween.pause();\n\n    if (_getTime() - wheelRefresh > 1000) {\n      // after 1 second, refresh the clamps otherwise that'll only happen when ScrollTrigger.refresh() is called or for touch-scrolling.\n      lastRefreshID = 0;\n      wheelRefresh = _getTime();\n    }\n  };\n\n  vars.onChange = function (self, dx, dy, xArray, yArray) {\n    _refreshID !== lastRefreshID && updateClamps();\n    dx && normalizeScrollX && scrollFuncX(scrollClampX(xArray[2] === dx ? startScrollX + (self.startX - self.x) : scrollFuncX() + dx - xArray[1])); // for more precision, we track pointer/touch movement from the start, otherwise it'll drift.\n\n    if (dy) {\n      scrollFuncY.offset && removeContentOffset();\n      var isTouch = yArray[2] === dy,\n          y = isTouch ? startScrollY + self.startY - self.y : scrollFuncY() + dy - yArray[1],\n          yClamped = scrollClampY(y);\n      isTouch && y !== yClamped && (startScrollY += yClamped - y);\n      scrollFuncY(yClamped);\n    }\n\n    (dy || dx) && _updateAll();\n  };\n\n  vars.onEnable = function () {\n    _allowNativePanning(target, normalizeScrollX ? false : \"x\");\n\n    ScrollTrigger.addEventListener(\"refresh\", onResize);\n\n    _addListener(_win, \"resize\", onResize);\n\n    if (scrollFuncY.smooth) {\n      scrollFuncY.target.style.scrollBehavior = \"auto\";\n      scrollFuncY.smooth = scrollFuncX.smooth = false;\n    }\n\n    inputObserver.enable();\n  };\n\n  vars.onDisable = function () {\n    _allowNativePanning(target, true);\n\n    _removeListener(_win, \"resize\", onResize);\n\n    ScrollTrigger.removeEventListener(\"refresh\", onResize);\n    inputObserver.kill();\n  };\n\n  vars.lockAxis = vars.lockAxis !== false;\n  self = new _Observer_js__WEBPACK_IMPORTED_MODULE_0__.Observer(vars);\n  self.iOS = _fixIOSBug; // used in the Observer getCachedScroll() function to work around an iOS bug that wreaks havoc with TouchEvent.clientY if we allow scroll to go all the way back to 0.\n\n  _fixIOSBug && !scrollFuncY() && scrollFuncY(1); // iOS bug causes event.clientY values to freak out (wildly inaccurate) if the scroll position is exactly 0.\n\n  _fixIOSBug && gsap.ticker.add(_passThrough); // prevent the ticker from sleeping\n\n  onStopDelayedCall = self._dc;\n  tween = gsap.to(self, {\n    ease: \"power4\",\n    paused: true,\n    inherit: false,\n    scrollX: normalizeScrollX ? \"+=0.1\" : \"+=0\",\n    scrollY: \"+=0.1\",\n    modifiers: {\n      scrollY: _interruptionTracker(scrollFuncY, scrollFuncY(), function () {\n        return tween.pause();\n      })\n    },\n    onUpdate: _updateAll,\n    onComplete: onStopDelayedCall.vars.onComplete\n  }); // we need the modifier to sense if the scroll position is altered outside of the momentum tween (like with a scrollTo tween) so we can pause() it to prevent conflicts.\n\n  return self;\n};\n\nScrollTrigger.sort = function (func) {\n  if (_isFunction(func)) {\n    return _triggers.sort(func);\n  }\n\n  var scroll = _win.pageYOffset || 0;\n  ScrollTrigger.getAll().forEach(function (t) {\n    return t._sortY = t.trigger ? scroll + t.trigger.getBoundingClientRect().top : t.start + _win.innerHeight;\n  });\n  return _triggers.sort(func || function (a, b) {\n    return (a.vars.refreshPriority || 0) * -1e6 + (a.vars.containerAnimation ? 1e6 : a._sortY) - ((b.vars.containerAnimation ? 1e6 : b._sortY) + (b.vars.refreshPriority || 0) * -1e6);\n  }); // anything with a containerAnimation should refresh last.\n};\n\nScrollTrigger.observe = function (vars) {\n  return new _Observer_js__WEBPACK_IMPORTED_MODULE_0__.Observer(vars);\n};\n\nScrollTrigger.normalizeScroll = function (vars) {\n  if (typeof vars === \"undefined\") {\n    return _normalizer;\n  }\n\n  if (vars === true && _normalizer) {\n    return _normalizer.enable();\n  }\n\n  if (vars === false) {\n    _normalizer && _normalizer.kill();\n    _normalizer = vars;\n    return;\n  }\n\n  var normalizer = vars instanceof _Observer_js__WEBPACK_IMPORTED_MODULE_0__.Observer ? vars : _getScrollNormalizer(vars);\n  _normalizer && _normalizer.target === normalizer.target && _normalizer.kill();\n  _isViewport(normalizer.target) && (_normalizer = normalizer);\n  return normalizer;\n};\n\nScrollTrigger.core = {\n  // smaller file size way to leverage in ScrollSmoother and Observer\n  _getVelocityProp: _Observer_js__WEBPACK_IMPORTED_MODULE_0__._getVelocityProp,\n  _inputObserver: _inputObserver,\n  _scrollers: _Observer_js__WEBPACK_IMPORTED_MODULE_0__._scrollers,\n  _proxies: _Observer_js__WEBPACK_IMPORTED_MODULE_0__._proxies,\n  bridge: {\n    // when normalizeScroll sets the scroll position (ss = setScroll)\n    ss: function ss() {\n      _lastScrollTime || _dispatch(\"scrollStart\");\n      _lastScrollTime = _getTime();\n    },\n    // a way to get the _refreshing value in Observer\n    ref: function ref() {\n      return _refreshing;\n    }\n  }\n};\n_getGSAP() && gsap.registerPlugin(ScrollTrigger);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/gsap/ScrollTrigger.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/code.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Code)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"m16 18 6-6-6-6\",\n            key: \"eg8j8\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m8 6-6 6 6 6\",\n            key: \"ppft3o\"\n        }\n    ]\n];\nconst Code = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"code\", __iconNode);\n //# sourceMappingURL=code.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js":
/*!********************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/graduation-cap.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ GraduationCap)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z\",\n            key: \"j76jl0\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M22 10v6\",\n            key: \"1lu8f3\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M6 12.5V16a6 3 0 0 0 12 0v-3.5\",\n            key: \"1r8lef\"\n        }\n    ]\n];\nconst GraduationCap = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"graduation-cap\", __iconNode);\n //# sourceMappingURL=graduation-cap.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvZ3JhZHVhdGlvbi1jYXAuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBR08sTUFBTSxVQUF1QjtJQUNsQztRQUNFO1FBQ0E7WUFDRSxDQUFHO1lBQ0gsR0FBSztRQUFBO0tBRVQ7SUFDQTtRQUFDLE1BQVE7UUFBQTtZQUFFLEdBQUcsQ0FBWTtZQUFBLEtBQUs7UUFBQSxDQUFVO0tBQUE7SUFDekM7UUFBQyxDQUFRO1FBQUEsQ0FBRTtZQUFBLEVBQUcsaUNBQWtDO1lBQUEsSUFBSztRQUFVO0tBQUE7Q0FDakU7QUFhTSxvQkFBZ0Isa0VBQWlCLG1CQUFrQixDQUFVIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEhQXFxzcmNcXGljb25zXFxncmFkdWF0aW9uLWNhcC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uJztcbmltcG9ydCB7IEljb25Ob2RlIH0gZnJvbSAnLi4vdHlwZXMnO1xuXG5leHBvcnQgY29uc3QgX19pY29uTm9kZTogSWNvbk5vZGUgPSBbXG4gIFtcbiAgICAncGF0aCcsXG4gICAge1xuICAgICAgZDogJ00yMS40MiAxMC45MjJhMSAxIDAgMCAwLS4wMTktMS44MzhMMTIuODMgNS4xOGEyIDIgMCAwIDAtMS42NiAwTDIuNiA5LjA4YTEgMSAwIDAgMCAwIDEuODMybDguNTcgMy45MDhhMiAyIDAgMCAwIDEuNjYgMHonLFxuICAgICAga2V5OiAnajc2amwwJyxcbiAgICB9LFxuICBdLFxuICBbJ3BhdGgnLCB7IGQ6ICdNMjIgMTB2NicsIGtleTogJzFsdThmMycgfV0sXG4gIFsncGF0aCcsIHsgZDogJ002IDEyLjVWMTZhNiAzIDAgMCAwIDEyIDB2LTMuNScsIGtleTogJzFyOGxlZicgfV0sXG5dO1xuXG4vKipcbiAqIEBjb21wb25lbnQgQG5hbWUgR3JhZHVhdGlvbkNhcFxuICogQGRlc2NyaXB0aW9uIEx1Y2lkZSBTVkcgaWNvbiBjb21wb25lbnQsIHJlbmRlcnMgU1ZHIEVsZW1lbnQgd2l0aCBjaGlsZHJlbi5cbiAqXG4gKiBAcHJldmlldyAhW2ltZ10oZGF0YTppbWFnZS9zdmcreG1sO2Jhc2U2NCxQSE4yWnlBZ2VHMXNibk05SW1oMGRIQTZMeTkzZDNjdWR6TXViM0puTHpJd01EQXZjM1puSWdvZ0lIZHBaSFJvUFNJeU5DSUtJQ0JvWldsbmFIUTlJakkwSWdvZ0lIWnBaWGRDYjNnOUlqQWdNQ0F5TkNBeU5DSUtJQ0JtYVd4c1BTSnViMjVsSWdvZ0lITjBjbTlyWlQwaUl6QXdNQ0lnYzNSNWJHVTlJbUpoWTJ0bmNtOTFibVF0WTI5c2IzSTZJQ05tWm1ZN0lHSnZjbVJsY2kxeVlXUnBkWE02SURKd2VDSUtJQ0J6ZEhKdmEyVXRkMmxrZEdnOUlqSWlDaUFnYzNSeWIydGxMV3hwYm1WallYQTlJbkp2ZFc1a0lnb2dJSE4wY205clpTMXNhVzVsYW05cGJqMGljbTkxYm1RaUNqNEtJQ0E4Y0dGMGFDQmtQU0pOTWpFdU5ESWdNVEF1T1RJeVlURWdNU0F3SURBZ01DMHVNREU1TFRFdU9ETTRUREV5TGpneklEVXVNVGhoTWlBeUlEQWdNQ0F3TFRFdU5qWWdNRXd5TGpZZ09TNHdPR0V4SURFZ01DQXdJREFnTUNBeExqZ3pNbXc0TGpVM0lETXVPVEE0WVRJZ01pQXdJREFnTUNBeExqWTJJREI2SWlBdlBnb2dJRHh3WVhSb0lHUTlJazB5TWlBeE1IWTJJaUF2UGdvZ0lEeHdZWFJvSUdROUlrMDJJREV5TGpWV01UWmhOaUF6SURBZ01DQXdJREV5SURCMkxUTXVOU0lnTHo0S1BDOXpkbWMrQ2c9PSkgLSBodHRwczovL2x1Y2lkZS5kZXYvaWNvbnMvZ3JhZHVhdGlvbi1jYXBcbiAqIEBzZWUgaHR0cHM6Ly9sdWNpZGUuZGV2L2d1aWRlL3BhY2thZ2VzL2x1Y2lkZS1yZWFjdCAtIERvY3VtZW50YXRpb25cbiAqXG4gKiBAcGFyYW0ge09iamVjdH0gcHJvcHMgLSBMdWNpZGUgaWNvbnMgcHJvcHMgYW5kIGFueSB2YWxpZCBTVkcgYXR0cmlidXRlXG4gKiBAcmV0dXJucyB7SlNYLkVsZW1lbnR9IEpTWCBFbGVtZW50XG4gKlxuICovXG5jb25zdCBHcmFkdWF0aW9uQ2FwID0gY3JlYXRlTHVjaWRlSWNvbignZ3JhZHVhdGlvbi1jYXAnLCBfX2ljb25Ob2RlKTtcblxuZXhwb3J0IGRlZmF1bHQgR3JhZHVhdGlvbkNhcDtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js":
/*!***********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/heart.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Heart)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z\",\n            key: \"c3ymky\"\n        }\n    ]\n];\nconst Heart = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"heart\", __iconNode);\n //# sourceMappingURL=heart.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js":
/*!***************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/lightbulb.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Lightbulb)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5\",\n            key: \"1gvzjb\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M9 18h6\",\n            key: \"x1upvd\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M10 22h4\",\n            key: \"ceow96\"\n        }\n    ]\n];\nconst Lightbulb = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"lightbulb\", __iconNode);\n //# sourceMappingURL=lightbulb.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5COneDrive%5C%5CDesktop%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CAboutSection.tsx%22%2C%22ids%22%3A%5B%22AboutSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5COneDrive%5C%5CDesktop%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CHeroSection.tsx%22%2C%22ids%22%3A%5B%22HeroSection%22%5D%7D&server=false!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5COneDrive%5C%5CDesktop%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CAboutSection.tsx%22%2C%22ids%22%3A%5B%22AboutSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5COneDrive%5C%5CDesktop%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CHeroSection.tsx%22%2C%22ids%22%3A%5B%22HeroSection%22%5D%7D&server=false! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/sections/AboutSection.tsx */ \"(app-pages-browser)/./src/components/sections/AboutSection.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/sections/HeroSection.tsx */ \"(app-pages-browser)/./src/components/sections/HeroSection.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDSFAlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNwb3J0Zm9saW8lNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDc2VjdGlvbnMlNUMlNUNBYm91dFNlY3Rpb24udHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQWJvdXRTZWN0aW9uJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0hQJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDcG9ydGZvbGlvJTVDJTVDc3JjJTVDJTVDY29tcG9uZW50cyU1QyU1Q3NlY3Rpb25zJTVDJTVDSGVyb1NlY3Rpb24udHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIySGVyb1NlY3Rpb24lMjIlNUQlN0Qmc2VydmVyPWZhbHNlISIsIm1hcHBpbmdzIjoiQUFBQSw4TUFBK0o7QUFDL0o7QUFDQSw0TUFBNkoiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkFib3V0U2VjdGlvblwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXEhQXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxccG9ydGZvbGlvXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXHNlY3Rpb25zXFxcXEFib3V0U2VjdGlvbi50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkhlcm9TZWN0aW9uXCJdICovIFwiQzpcXFxcVXNlcnNcXFxcSFBcXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxwb3J0Zm9saW9cXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcc2VjdGlvbnNcXFxcSGVyb1NlY3Rpb24udHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5COneDrive%5C%5CDesktop%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CAboutSection.tsx%22%2C%22ids%22%3A%5B%22AboutSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5COneDrive%5C%5CDesktop%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CHeroSection.tsx%22%2C%22ids%22%3A%5B%22HeroSection%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/sections/AboutSection.tsx":
/*!**************************************************!*\
  !*** ./src/components/sections/AboutSection.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AboutSection: () => (/* binding */ AboutSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Code_GraduationCap_Heart_Lightbulb_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Code,GraduationCap,Heart,Lightbulb!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Code_GraduationCap_Heart_Lightbulb_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Code,GraduationCap,Heart,Lightbulb!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_Code_GraduationCap_Heart_Lightbulb_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Code,GraduationCap,Heart,Lightbulb!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_Code_GraduationCap_Heart_Lightbulb_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Code,GraduationCap,Heart,Lightbulb!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var gsap__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! gsap */ \"(app-pages-browser)/./node_modules/gsap/index.js\");\n/* harmony import */ var gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! gsap/ScrollTrigger */ \"(app-pages-browser)/./node_modules/gsap/ScrollTrigger.js\");\n/* __next_internal_client_entry_do_not_use__ AboutSection auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nif (true) {\n    gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.registerPlugin(gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_3__.ScrollTrigger);\n}\nfunction AboutSection() {\n    _s();\n    const sectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const titleRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const contentRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const cardsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AboutSection.useEffect\": ()=>{\n            const ctx = gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.context({\n                \"AboutSection.useEffect.ctx\": ()=>{\n                    var _cardsRef_current;\n                    // Title animation\n                    gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.fromTo(titleRef.current, {\n                        opacity: 0,\n                        y: 50\n                    }, {\n                        opacity: 1,\n                        y: 0,\n                        duration: 1,\n                        ease: \"power3.out\",\n                        scrollTrigger: {\n                            trigger: titleRef.current,\n                            start: \"top 80%\",\n                            end: \"bottom 20%\",\n                            toggleActions: \"play none none reverse\"\n                        }\n                    });\n                    // Content animation\n                    gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.fromTo(contentRef.current, {\n                        opacity: 0,\n                        y: 30\n                    }, {\n                        opacity: 1,\n                        y: 0,\n                        duration: 0.8,\n                        ease: \"power3.out\",\n                        scrollTrigger: {\n                            trigger: contentRef.current,\n                            start: \"top 80%\",\n                            end: \"bottom 20%\",\n                            toggleActions: \"play none none reverse\"\n                        }\n                    });\n                    // Cards stagger animation\n                    gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.fromTo(((_cardsRef_current = cardsRef.current) === null || _cardsRef_current === void 0 ? void 0 : _cardsRef_current.children) || [], {\n                        opacity: 0,\n                        y: 50,\n                        scale: 0.9\n                    }, {\n                        opacity: 1,\n                        y: 0,\n                        scale: 1,\n                        duration: 0.6,\n                        ease: \"power3.out\",\n                        stagger: 0.2,\n                        scrollTrigger: {\n                            trigger: cardsRef.current,\n                            start: \"top 80%\",\n                            end: \"bottom 20%\",\n                            toggleActions: \"play none none reverse\"\n                        }\n                    });\n                }\n            }[\"AboutSection.useEffect.ctx\"], sectionRef);\n            return ({\n                \"AboutSection.useEffect\": ()=>ctx.revert()\n            })[\"AboutSection.useEffect\"];\n        }\n    }[\"AboutSection.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"about\",\n        ref: sectionRef,\n        className: \"py-20 bg-white dark:bg-gray-900\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        ref: titleRef,\n                        className: \"text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4\",\n                        children: [\n                            \"About \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"gradient-text\",\n                                children: \"Me\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 19\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-2 gap-12 items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: contentRef,\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"prose prose-lg dark:prose-invert max-w-none\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 dark:text-gray-300 leading-relaxed\",\n                                        children: [\n                                            \"Hello! I'm Aman, a passionate MERN Stack Developer currently pursuing my\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                className: \"text-blue-600 dark:text-blue-400\",\n                                                children: \" Master of Computer Applications (MCA) at Thapar University\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 17\n                                            }, this),\n                                            \". My journey in web development started with curiosity and has evolved into a deep passion for creating meaningful digital experiences.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 dark:text-gray-300 leading-relaxed\",\n                                        children: \"I specialize in building modern, responsive web applications using the MERN stack (MongoDB, Express.js, React, Node.js). What drives me is the ability to transform ideas into functional, beautiful applications that solve real-world problems.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 dark:text-gray-300 leading-relaxed\",\n                                        children: [\n                                            \"Currently, I'm working on my capstone project focused on\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                className: \"text-purple-600 dark:text-purple-400\",\n                                                children: \" Multimodal Deepfake Detection\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 17\n                                            }, this),\n                                            \", exploring the intersection of AI and cybersecurity. This project combines my technical skills with cutting-edge research in machine learning and computer vision.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 dark:text-gray-300 leading-relaxed\",\n                                        children: \"When I'm not coding, you'll find me exploring new technologies, contributing to open-source projects, or sharing my learning journey through technical blogs. I believe in continuous learning and staying updated with the latest trends in web development.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: cardsRef,\n                            className: \"grid gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 p-6 rounded-xl border border-blue-200 dark:border-blue-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-blue-600 rounded-lg mr-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Code_GraduationCap_Heart_Lightbulb_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"h-6 w-6 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-semibold text-gray-900 dark:text-white\",\n                                                    children: \"Development Focus\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                    lineNumber: 135,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-300\",\n                                            children: \"Specializing in MERN Stack development with a focus on clean, scalable code and modern web technologies.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 p-6 rounded-xl border border-purple-200 dark:border-purple-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-purple-600 rounded-lg mr-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Code_GraduationCap_Heart_Lightbulb_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"h-6 w-6 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-semibold text-gray-900 dark:text-white\",\n                                                    children: \"Education\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-300\",\n                                            children: \"MCA student at Thapar University, combining academic excellence with practical development experience.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 p-6 rounded-xl border border-green-200 dark:border-green-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-green-600 rounded-lg mr-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Code_GraduationCap_Heart_Lightbulb_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-6 w-6 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-semibold text-gray-900 dark:text-white\",\n                                                    children: \"Current Research\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-300\",\n                                            children: \"Working on Multimodal Deepfake Detection, exploring AI and machine learning applications in cybersecurity.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-r from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 p-6 rounded-xl border border-red-200 dark:border-red-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-red-600 rounded-lg mr-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Code_GraduationCap_Heart_Lightbulb_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-6 w-6 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-semibold text-gray-900 dark:text-white\",\n                                                    children: \"Passion\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-300\",\n                                            children: \"Passionate about creating impactful web applications and contributing to the open-source community.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n            lineNumber: 86,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, this);\n}\n_s(AboutSection, \"2izF2vk8fx2znajRku2WKq574EY=\");\n_c = AboutSection;\nvar _c;\n$RefreshReg$(_c, \"AboutSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/AboutSection.tsx\n"));

/***/ })

});