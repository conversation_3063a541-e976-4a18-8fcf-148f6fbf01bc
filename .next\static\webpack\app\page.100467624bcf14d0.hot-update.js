/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5COneDrive%5C%5CDesktop%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CAboutSection.tsx%22%2C%22ids%22%3A%5B%22AboutSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5COneDrive%5C%5CDesktop%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CHeroSection.tsx%22%2C%22ids%22%3A%5B%22HeroSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5COneDrive%5C%5CDesktop%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CSkillsSection.tsx%22%2C%22ids%22%3A%5B%22SkillsSection%22%5D%7D&server=false!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5COneDrive%5C%5CDesktop%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CAboutSection.tsx%22%2C%22ids%22%3A%5B%22AboutSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5COneDrive%5C%5CDesktop%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CHeroSection.tsx%22%2C%22ids%22%3A%5B%22HeroSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5COneDrive%5C%5CDesktop%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CSkillsSection.tsx%22%2C%22ids%22%3A%5B%22SkillsSection%22%5D%7D&server=false! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/sections/AboutSection.tsx */ \"(app-pages-browser)/./src/components/sections/AboutSection.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/sections/HeroSection.tsx */ \"(app-pages-browser)/./src/components/sections/HeroSection.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/sections/SkillsSection.tsx */ \"(app-pages-browser)/./src/components/sections/SkillsSection.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDSFAlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNwb3J0Zm9saW8lNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDc2VjdGlvbnMlNUMlNUNBYm91dFNlY3Rpb24udHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQWJvdXRTZWN0aW9uJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0hQJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDcG9ydGZvbGlvJTVDJTVDc3JjJTVDJTVDY29tcG9uZW50cyU1QyU1Q3NlY3Rpb25zJTVDJTVDSGVyb1NlY3Rpb24udHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIySGVyb1NlY3Rpb24lMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDSFAlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNwb3J0Zm9saW8lNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDc2VjdGlvbnMlNUMlNUNTa2lsbHNTZWN0aW9uLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlNraWxsc1NlY3Rpb24lMjIlNUQlN0Qmc2VydmVyPWZhbHNlISIsIm1hcHBpbmdzIjoiQUFBQSw4TUFBK0o7QUFDL0o7QUFDQSw0TUFBNko7QUFDN0o7QUFDQSxnTkFBaUsiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkFib3V0U2VjdGlvblwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXEhQXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxccG9ydGZvbGlvXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXHNlY3Rpb25zXFxcXEFib3V0U2VjdGlvbi50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkhlcm9TZWN0aW9uXCJdICovIFwiQzpcXFxcVXNlcnNcXFxcSFBcXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxwb3J0Zm9saW9cXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcc2VjdGlvbnNcXFxcSGVyb1NlY3Rpb24udHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJTa2lsbHNTZWN0aW9uXCJdICovIFwiQzpcXFxcVXNlcnNcXFxcSFBcXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxwb3J0Zm9saW9cXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcc2VjdGlvbnNcXFxcU2tpbGxzU2VjdGlvbi50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5COneDrive%5C%5CDesktop%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CAboutSection.tsx%22%2C%22ids%22%3A%5B%22AboutSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5COneDrive%5C%5CDesktop%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CHeroSection.tsx%22%2C%22ids%22%3A%5B%22HeroSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5COneDrive%5C%5CDesktop%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CSkillsSection.tsx%22%2C%22ids%22%3A%5B%22SkillsSection%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/sections/SkillsSection.tsx":
/*!***************************************************!*\
  !*** ./src/components/sections/SkillsSection.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SkillsSection: () => (/* binding */ SkillsSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var gsap__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! gsap */ \"(app-pages-browser)/./node_modules/gsap/index.js\");\n/* harmony import */ var gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! gsap/ScrollTrigger */ \"(app-pages-browser)/./node_modules/gsap/ScrollTrigger.js\");\n/* __next_internal_client_entry_do_not_use__ SkillsSection auto */ \nvar _s = $RefreshSig$();\n\n\n\nif (true) {\n    gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.registerPlugin(gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_3__.ScrollTrigger);\n}\nconst skillsData = {\n    Frontend: [\n        {\n            name: \"React\",\n            level: 90,\n            color: \"bg-blue-500\"\n        },\n        {\n            name: \"JavaScript\",\n            level: 85,\n            color: \"bg-yellow-500\"\n        },\n        {\n            name: \"HTML5\",\n            level: 95,\n            color: \"bg-orange-500\"\n        },\n        {\n            name: \"CSS3\",\n            level: 90,\n            color: \"bg-blue-600\"\n        },\n        {\n            name: \"Tailwind CSS\",\n            level: 85,\n            color: \"bg-teal-500\"\n        },\n        {\n            name: \"TypeScript\",\n            level: 75,\n            color: \"bg-blue-700\"\n        }\n    ],\n    Backend: [\n        {\n            name: \"Node.js\",\n            level: 85,\n            color: \"bg-green-600\"\n        },\n        {\n            name: \"Express.js\",\n            level: 80,\n            color: \"bg-gray-600\"\n        },\n        {\n            name: \"REST APIs\",\n            level: 85,\n            color: \"bg-purple-500\"\n        },\n        {\n            name: \"Authentication\",\n            level: 75,\n            color: \"bg-red-500\"\n        }\n    ],\n    Database: [\n        {\n            name: \"MongoDB\",\n            level: 80,\n            color: \"bg-green-500\"\n        },\n        {\n            name: \"Mongoose\",\n            level: 75,\n            color: \"bg-green-600\"\n        },\n        {\n            name: \"MySQL\",\n            level: 70,\n            color: \"bg-blue-500\"\n        }\n    ],\n    Tools: [\n        {\n            name: \"Git & GitHub\",\n            level: 85,\n            color: \"bg-gray-800\"\n        },\n        {\n            name: \"VS Code\",\n            level: 90,\n            color: \"bg-blue-600\"\n        },\n        {\n            name: \"Postman\",\n            level: 80,\n            color: \"bg-orange-500\"\n        },\n        {\n            name: \"Netlify/Vercel\",\n            level: 75,\n            color: \"bg-black\"\n        },\n        {\n            name: \"npm/yarn\",\n            level: 80,\n            color: \"bg-red-600\"\n        }\n    ]\n};\nfunction SkillsSection() {\n    _s();\n    const sectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const titleRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const categoriesRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SkillsSection.useEffect\": ()=>{\n            const ctx = gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.context({\n                \"SkillsSection.useEffect.ctx\": ()=>{\n                    var _categoriesRef_current;\n                    // Title animation\n                    gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.fromTo(titleRef.current, {\n                        opacity: 0,\n                        y: 50\n                    }, {\n                        opacity: 1,\n                        y: 0,\n                        duration: 1,\n                        ease: \"power3.out\",\n                        scrollTrigger: {\n                            trigger: titleRef.current,\n                            start: \"top 80%\",\n                            end: \"bottom 20%\",\n                            toggleActions: \"play none none reverse\"\n                        }\n                    });\n                    // Categories animation\n                    gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.fromTo(((_categoriesRef_current = categoriesRef.current) === null || _categoriesRef_current === void 0 ? void 0 : _categoriesRef_current.children) || [], {\n                        opacity: 0,\n                        y: 50\n                    }, {\n                        opacity: 1,\n                        y: 0,\n                        duration: 0.8,\n                        ease: \"power3.out\",\n                        stagger: 0.2,\n                        scrollTrigger: {\n                            trigger: categoriesRef.current,\n                            start: \"top 80%\",\n                            end: \"bottom 20%\",\n                            toggleActions: \"play none none reverse\"\n                        }\n                    });\n                    // Skill bars animation\n                    Object.keys(skillsData).forEach({\n                        \"SkillsSection.useEffect.ctx\": (category, categoryIndex)=>{\n                            var _categoriesRef_current;\n                            const categoryElement = (_categoriesRef_current = categoriesRef.current) === null || _categoriesRef_current === void 0 ? void 0 : _categoriesRef_current.children[categoryIndex];\n                            const skillBars = categoryElement === null || categoryElement === void 0 ? void 0 : categoryElement.querySelectorAll('.skill-bar');\n                            if (skillBars) {\n                                skillBars.forEach({\n                                    \"SkillsSection.useEffect.ctx\": (bar, index)=>{\n                                        const progressBar = bar.querySelector('.progress-bar');\n                                        const skill = skillsData[category][index];\n                                        if (progressBar) {\n                                            gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.fromTo(progressBar, {\n                                                width: \"0%\"\n                                            }, {\n                                                width: \"\".concat(skill.level, \"%\"),\n                                                duration: 1.5,\n                                                ease: \"power3.out\",\n                                                delay: 0.5 + index * 0.1,\n                                                scrollTrigger: {\n                                                    trigger: bar,\n                                                    start: \"top 90%\",\n                                                    end: \"bottom 10%\",\n                                                    toggleActions: \"play none none reverse\"\n                                                }\n                                            });\n                                        }\n                                    }\n                                }[\"SkillsSection.useEffect.ctx\"]);\n                            }\n                        }\n                    }[\"SkillsSection.useEffect.ctx\"]);\n                }\n            }[\"SkillsSection.useEffect.ctx\"], sectionRef);\n            return ({\n                \"SkillsSection.useEffect\": ()=>ctx.revert()\n            })[\"SkillsSection.useEffect\"];\n        }\n    }[\"SkillsSection.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"skills\",\n        ref: sectionRef,\n        className: \"py-20 bg-gray-50 dark:bg-gray-800\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            ref: titleRef,\n                            className: \"text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4\",\n                            children: [\n                                \"My \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"gradient-text\",\n                                    children: \"Skills\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\SkillsSection.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 16\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\SkillsSection.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto\",\n                            children: \"Here are the technologies and tools I work with to bring ideas to life\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\SkillsSection.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\SkillsSection.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    ref: categoriesRef,\n                    className: \"grid md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                    children: Object.entries(skillsData).map((param)=>{\n                        let [category, skills] = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-900 p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-gray-900 dark:text-white mb-6 text-center\",\n                                    children: category\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\SkillsSection.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: skills.map((skill, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"skill-bar\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                                            children: skill.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\SkillsSection.tsx\",\n                                                            lineNumber: 152,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                            children: [\n                                                                skill.level,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\SkillsSection.tsx\",\n                                                            lineNumber: 155,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\SkillsSection.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"progress-bar h-2 rounded-full \".concat(skill.color, \" transition-all duration-300\"),\n                                                        style: {\n                                                            width: \"0%\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\SkillsSection.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\SkillsSection.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, skill.name, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\SkillsSection.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\SkillsSection.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, category, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\SkillsSection.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 13\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\SkillsSection.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-16 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-semibold text-gray-900 dark:text-white mb-8\",\n                            children: \"Additional Technologies\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\SkillsSection.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap justify-center gap-4\",\n                            children: [\n                                \"Python\",\n                                \"Machine Learning\",\n                                \"Computer Vision\",\n                                \"Docker\",\n                                \"Linux\",\n                                \"Figma\",\n                                \"Photoshop\",\n                                \"Responsive Design\",\n                                \"SEO\",\n                                \"Performance Optimization\"\n                            ].map((tech)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-4 py-2 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded-full text-sm font-medium hover:bg-blue-200 dark:hover:bg-blue-900/50 transition-colors duration-200\",\n                                    children: tech\n                                }, tech, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\SkillsSection.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\SkillsSection.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\SkillsSection.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\SkillsSection.tsx\",\n            lineNumber: 126,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\sections\\\\SkillsSection.tsx\",\n        lineNumber: 121,\n        columnNumber: 5\n    }, this);\n}\n_s(SkillsSection, \"9zes8xYST4iz/y4vpwia81TXuMY=\");\n_c = SkillsSection;\nvar _c;\n$RefreshReg$(_c, \"SkillsSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/SkillsSection.tsx\n"));

/***/ })

});