{"name": "portfolio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "framer-motion": "^12.23.9", "gsap": "^3.13.0", "lucide-react": "^0.525.0", "next": "15.4.3", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.61.1", "zod": "^4.0.8"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.3", "tailwindcss": "^4", "typescript": "^5"}}