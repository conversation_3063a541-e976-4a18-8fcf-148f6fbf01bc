"use client";

import { useEffect, useRef } from "react";
import { Calendar, MapPin, Award, Users, Code, Trophy } from "lucide-react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

if (typeof window !== "undefined") {
  gsap.registerPlugin(ScrollTrigger);
}

const experiences = [
  {
    id: 1,
    type: "internship",
    title: "Frontend Developer Intern",
    company: "Tech Solutions Pvt Ltd",
    location: "Remote",
    duration: "Jun 2024 - Aug 2024",
    description: "Developed responsive web applications using React and collaborated with the design team to implement user-friendly interfaces.",
    achievements: [
      "Built 3 responsive web applications",
      "Improved page load speed by 40%",
      "Collaborated with a team of 5 developers",
      "Implemented modern UI/UX designs"
    ],
    technologies: ["React", "JavaScript", "CSS3", "Git", "Figma"],
    icon: Code
  },
  {
    id: 2,
    type: "hackathon",
    title: "Smart City Hackathon",
    company: "Thapar University",
    location: "Patiala, Punjab",
    duration: "Mar 2024",
    description: "Developed an IoT-based smart traffic management system that won 2nd place among 50+ teams.",
    achievements: [
      "2nd Place Winner",
      "Developed IoT solution for traffic management",
      "Led a team of 4 members",
      "Presented to industry experts"
    ],
    technologies: ["React", "Node.js", "IoT", "MongoDB", "Arduino"],
    icon: Trophy
  },
  {
    id: 3,
    type: "project",
    title: "College Management System",
    company: "Academic Project",
    location: "Thapar University",
    duration: "Jan 2024 - May 2024",
    description: "Led the development of a comprehensive college management system as part of the software engineering course.",
    achievements: [
      "Led a team of 6 students",
      "Implemented complete CRUD operations",
      "Designed database architecture",
      "Achieved 95% project grade"
    ],
    technologies: ["React", "Node.js", "MongoDB", "Express", "JWT"],
    icon: Users
  },
  {
    id: 4,
    type: "hackathon",
    title: "AI Innovation Challenge",
    company: "National Level Competition",
    location: "Delhi, India",
    duration: "Nov 2023",
    description: "Participated in a 48-hour hackathon focused on AI solutions for healthcare, developing a symptom checker application.",
    achievements: [
      "Top 10 Finalist",
      "Developed AI-powered symptom checker",
      "Integrated machine learning models",
      "Pitched to healthcare professionals"
    ],
    technologies: ["Python", "TensorFlow", "React", "Flask", "scikit-learn"],
    icon: Award
  },
  {
    id: 5,
    type: "volunteer",
    title: "Technical Volunteer",
    company: "CodeFest 2023",
    location: "Thapar University",
    duration: "Oct 2023",
    description: "Volunteered as a technical coordinator for the annual coding festival, managing technical events and workshops.",
    achievements: [
      "Coordinated 5+ technical events",
      "Managed 200+ participants",
      "Organized coding workshops",
      "Mentored junior students"
    ],
    technologies: ["Event Management", "Technical Coordination", "Mentoring"],
    icon: Users
  }
];

export function ExperienceSection() {
  const sectionRef = useRef<HTMLElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const timelineRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Title animation
      gsap.fromTo(
        titleRef.current,
        { opacity: 0, y: 50 },
        {
          opacity: 1,
          y: 0,
          duration: 1,
          ease: "power3.out",
          scrollTrigger: {
            trigger: titleRef.current,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse",
          },
        }
      );

      // Timeline items animation
      gsap.fromTo(
        timelineRef.current?.children || [],
        { opacity: 0, x: -50 },
        {
          opacity: 1,
          x: 0,
          duration: 0.8,
          ease: "power3.out",
          stagger: 0.2,
          scrollTrigger: {
            trigger: timelineRef.current,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse",
          },
        }
      );
    }, sectionRef);

    return () => ctx.revert();
  }, []);

  const getTypeColor = (type: string) => {
    switch (type) {
      case "internship":
        return "bg-blue-500";
      case "hackathon":
        return "bg-purple-500";
      case "project":
        return "bg-green-500";
      case "volunteer":
        return "bg-orange-500";
      default:
        return "bg-gray-500";
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case "internship":
        return "Internship";
      case "hackathon":
        return "Hackathon";
      case "project":
        return "Academic Project";
      case "volunteer":
        return "Volunteer Work";
      default:
        return "Experience";
    }
  };

  return (
    <section
      id="experience"
      ref={sectionRef}
      className="py-20 bg-gray-50 dark:bg-gray-800"
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2
            ref={titleRef}
            className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4"
          >
            My <span className="gradient-text">Experience</span>
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            My journey through internships, hackathons, academic projects, and volunteer work
          </p>
        </div>

        <div ref={timelineRef} className="relative">
          {/* Timeline Line */}
          <div className="absolute left-8 md:left-1/2 transform md:-translate-x-1/2 w-0.5 h-full bg-gray-300 dark:bg-gray-600"></div>

          {experiences.map((experience, index) => {
            const IconComponent = experience.icon;
            return (
              <div
                key={experience.id}
                className={`relative flex items-center mb-12 ${
                  index % 2 === 0 ? "md:flex-row" : "md:flex-row-reverse"
                }`}
              >
                {/* Timeline Icon */}
                <div className="absolute left-8 md:left-1/2 transform -translate-x-1/2 w-16 h-16 bg-white dark:bg-gray-900 rounded-full border-4 border-gray-300 dark:border-gray-600 flex items-center justify-center z-10">
                  <div className={`w-8 h-8 ${getTypeColor(experience.type)} rounded-full flex items-center justify-center`}>
                    <IconComponent className="h-4 w-4 text-white" />
                  </div>
                </div>

                {/* Content */}
                <div className={`w-full md:w-5/12 ${index % 2 === 0 ? "md:pr-8" : "md:pl-8"} ml-20 md:ml-0`}>
                  <div className="bg-white dark:bg-gray-900 p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300">
                    {/* Header */}
                    <div className="flex items-center justify-between mb-3">
                      <span className={`px-3 py-1 rounded-full text-xs font-medium text-white ${getTypeColor(experience.type)}`}>
                        {getTypeLabel(experience.type)}
                      </span>
                      <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                        <Calendar className="h-4 w-4 mr-1" />
                        {experience.duration}
                      </div>
                    </div>

                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                      {experience.title}
                    </h3>

                    <div className="flex items-center text-gray-600 dark:text-gray-300 mb-3">
                      <span className="font-medium">{experience.company}</span>
                      <span className="mx-2">•</span>
                      <div className="flex items-center">
                        <MapPin className="h-4 w-4 mr-1" />
                        {experience.location}
                      </div>
                    </div>

                    <p className="text-gray-600 dark:text-gray-300 mb-4">
                      {experience.description}
                    </p>

                    {/* Achievements */}
                    <div className="mb-4">
                      <h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-2">
                        Key Achievements:
                      </h4>
                      <ul className="space-y-1">
                        {experience.achievements.map((achievement, idx) => (
                          <li key={idx} className="text-sm text-gray-600 dark:text-gray-300 flex items-start">
                            <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                            {achievement}
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* Technologies */}
                    <div className="flex flex-wrap gap-2">
                      {experience.technologies.map((tech) => (
                        <span
                          key={tech}
                          className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded text-xs font-medium"
                        >
                          {tech}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
}
