"use client";

import { useEffect, useRef } from "react";
import { GraduationCap, Calendar, MapPin, Award, BookOpen, Star } from "lucide-react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

if (typeof window !== "undefined") {
  gsap.registerPlugin(ScrollTrigger);
}

const education = [
  {
    id: 1,
    degree: "Master of Computer Applications (MCA)",
    institution: "Thapar Institute of Engineering & Technology",
    location: "Patiala, Punjab, India",
    duration: "2023 - 2025",
    cgpa: "8.5/10",
    status: "Pursuing",
    description: "Specializing in software development, data structures, algorithms, and emerging technologies. Currently working on capstone project in AI/ML domain.",
    subjects: [
      "Advanced Data Structures & Algorithms",
      "Software Engineering",
      "Database Management Systems",
      "Machine Learning",
      "Computer Networks",
      "Web Technologies"
    ],
    achievements: [
      "Dean's List for Academic Excellence",
      "Best Project Award for Semester 2",
      "Active member of Coding Club",
      "Participated in multiple hackathons"
    ]
  },
  {
    id: 2,
    degree: "Bachelor of Computer Applications (BCA)",
    institution: "Punjab University",
    location: "Chandigarh, Punjab, India",
    duration: "2020 - 2023",
    cgpa: "8.2/10",
    status: "Completed",
    description: "Comprehensive foundation in computer science fundamentals, programming languages, and software development methodologies.",
    subjects: [
      "Programming in C/C++",
      "Java Programming",
      "Database Systems",
      "Computer Graphics",
      "System Analysis & Design",
      "Operating Systems"
    ],
    achievements: [
      "Graduated with First Class",
      "Top 10% of the batch",
      "Led college tech fest organizing committee",
      "Published research paper on web security"
    ]
  }
];

const certifications = [
  {
    id: 1,
    title: "Full Stack Web Development",
    provider: "Coursera - Meta",
    date: "2024",
    credentialId: "ABC123XYZ",
    skills: ["React", "Node.js", "MongoDB", "Express.js"]
  },
  {
    id: 2,
    title: "Machine Learning Specialization",
    provider: "Coursera - Stanford University",
    date: "2024",
    credentialId: "ML456DEF",
    skills: ["Python", "TensorFlow", "Scikit-learn", "Neural Networks"]
  },
  {
    id: 3,
    title: "JavaScript Algorithms and Data Structures",
    provider: "freeCodeCamp",
    date: "2023",
    credentialId: "JS789GHI",
    skills: ["JavaScript", "Algorithms", "Data Structures", "Problem Solving"]
  },
  {
    id: 4,
    title: "AWS Cloud Practitioner",
    provider: "Amazon Web Services",
    date: "2023",
    credentialId: "AWS101JKL",
    skills: ["Cloud Computing", "AWS Services", "DevOps", "Infrastructure"]
  }
];

export function EducationSection() {
  const sectionRef = useRef<HTMLElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const educationRef = useRef<HTMLDivElement>(null);
  const certificationsRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Title animation
      gsap.fromTo(
        titleRef.current,
        { opacity: 0, y: 50 },
        {
          opacity: 1,
          y: 0,
          duration: 1,
          ease: "power3.out",
          scrollTrigger: {
            trigger: titleRef.current,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse",
          },
        }
      );

      // Education cards animation
      gsap.fromTo(
        educationRef.current?.children || [],
        { opacity: 0, y: 50 },
        {
          opacity: 1,
          y: 0,
          duration: 0.8,
          ease: "power3.out",
          stagger: 0.3,
          scrollTrigger: {
            trigger: educationRef.current,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse",
          },
        }
      );

      // Certifications animation
      gsap.fromTo(
        certificationsRef.current?.children || [],
        { opacity: 0, scale: 0.9 },
        {
          opacity: 1,
          scale: 1,
          duration: 0.6,
          ease: "power3.out",
          stagger: 0.1,
          scrollTrigger: {
            trigger: certificationsRef.current,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse",
          },
        }
      );
    }, sectionRef);

    return () => ctx.revert();
  }, []);

  return (
    <section
      id="education"
      ref={sectionRef}
      className="py-20 bg-white dark:bg-gray-900"
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2
            ref={titleRef}
            className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4"
          >
            My <span className="gradient-text">Education</span>
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Academic background, certifications, and continuous learning journey
          </p>
        </div>

        {/* Education */}
        <div ref={educationRef} className="mb-20">
          <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-8 text-center">
            Academic Qualifications
          </h3>
          <div className="space-y-8">
            {education.map((edu) => (
              <div
                key={edu.id}
                className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 p-8 rounded-xl border border-blue-200 dark:border-blue-800 hover:shadow-lg transition-shadow duration-300"
              >
                <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between">
                  <div className="flex-1">
                    <div className="flex items-center mb-4">
                      <div className="p-3 bg-blue-600 rounded-lg mr-4">
                        <GraduationCap className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <h4 className="text-xl font-semibold text-gray-900 dark:text-white">
                          {edu.degree}
                        </h4>
                        <p className="text-blue-600 dark:text-blue-400 font-medium">
                          {edu.institution}
                        </p>
                      </div>
                    </div>

                    <div className="flex flex-wrap items-center gap-4 mb-4 text-sm text-gray-600 dark:text-gray-300">
                      <div className="flex items-center">
                        <MapPin className="h-4 w-4 mr-1" />
                        {edu.location}
                      </div>
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-1" />
                        {edu.duration}
                      </div>
                      <div className="flex items-center">
                        <Star className="h-4 w-4 mr-1" />
                        CGPA: {edu.cgpa}
                      </div>
                      <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                        edu.status === 'Completed' 
                          ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                          : 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'
                      }`}>
                        {edu.status}
                      </span>
                    </div>

                    <p className="text-gray-600 dark:text-gray-300 mb-6">
                      {edu.description}
                    </p>

                    <div className="grid md:grid-cols-2 gap-6">
                      {/* Key Subjects */}
                      <div>
                        <h5 className="text-sm font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                          <BookOpen className="h-4 w-4 mr-2" />
                          Key Subjects
                        </h5>
                        <ul className="space-y-1">
                          {edu.subjects.map((subject, idx) => (
                            <li key={idx} className="text-sm text-gray-600 dark:text-gray-300 flex items-start">
                              <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                              {subject}
                            </li>
                          ))}
                        </ul>
                      </div>

                      {/* Achievements */}
                      <div>
                        <h5 className="text-sm font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                          <Award className="h-4 w-4 mr-2" />
                          Achievements
                        </h5>
                        <ul className="space-y-1">
                          {edu.achievements.map((achievement, idx) => (
                            <li key={idx} className="text-sm text-gray-600 dark:text-gray-300 flex items-start">
                              <span className="w-1.5 h-1.5 bg-purple-500 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                              {achievement}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Certifications */}
        <div>
          <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-8 text-center">
            Certifications & Courses
          </h3>
          <div ref={certificationsRef} className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {certifications.map((cert) => (
              <div
                key={cert.id}
                className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 border border-gray-200 dark:border-gray-700"
              >
                <div className="flex items-center mb-4">
                  <div className="p-2 bg-green-600 rounded-lg mr-3">
                    <Award className="h-5 w-5 text-white" />
                  </div>
                  <span className="text-sm text-green-600 dark:text-green-400 font-medium">
                    {cert.date}
                  </span>
                </div>

                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  {cert.title}
                </h4>

                <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
                  {cert.provider}
                </p>

                <div className="flex flex-wrap gap-1 mb-3">
                  {cert.skills.slice(0, 2).map((skill) => (
                    <span
                      key={skill}
                      className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded text-xs font-medium"
                    >
                      {skill}
                    </span>
                  ))}
                  {cert.skills.length > 2 && (
                    <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400 rounded text-xs">
                      +{cert.skills.length - 2}
                    </span>
                  )}
                </div>

                <p className="text-xs text-gray-500 dark:text-gray-400">
                  ID: {cert.credentialId}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
